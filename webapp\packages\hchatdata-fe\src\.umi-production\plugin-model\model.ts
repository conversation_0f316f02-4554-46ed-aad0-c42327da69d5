// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
import model_1 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/models/allUserData';
import model_2 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/models/databaseData';
import model_3 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/models/dimensionData';
import model_5 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/models/metricData';
import model_6 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/models/modelData';
import model_4 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/models/domainData';
import model_7 from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-initialState/@@initialState';

export const models = {
model_1: { namespace: 'allUserData', model: model_1 },
model_2: { namespace: 'SemanticModel.databaseData', model: model_2 },
model_3: { namespace: 'SemanticModel.dimensionData', model: model_3 },
model_5: { namespace: 'SemanticModel.metricData', model: model_5 },
model_6: { namespace: 'SemanticModel.modelData', model: model_6 },
model_4: { namespace: 'SemanticModel.domainData', model: model_4 },
model_7: { namespace: '@@initialState', model: model_7 },
} as const
