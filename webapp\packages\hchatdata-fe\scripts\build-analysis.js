#!/usr/bin/env node

/**
 * 构建分析脚本
 * 用于分析打包后的文件大小和优化效果
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeDistFiles() {
  const distPath = path.join(__dirname, '../dist');
  
  if (!fs.existsSync(distPath)) {
    colorLog('red', '❌ dist 目录不存在，请先运行构建命令');
    return;
  }

  colorLog('cyan', '📊 分析构建产物...\n');

  const files = fs.readdirSync(distPath);
  const jsFiles = files.filter(file => file.endsWith('.js') && !file.endsWith('.gz'));
  const cssFiles = files.filter(file => file.endsWith('.css') && !file.endsWith('.gz'));
  const gzFiles = files.filter(file => file.endsWith('.gz'));

  // 分析主要的JS文件
  colorLog('bright', '📦 主要 JavaScript 文件:');
  let totalJSSize = 0;
  
  jsFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalJSSize += size;
    
    let color = 'green';
    if (size > 1024 * 1024) { // > 1MB
      color = 'red';
    } else if (size > 500 * 1024) { // > 500KB
      color = 'yellow';
    }
    
    colorLog(color, `  ${file}: ${formatBytes(size)}`);
  });

  colorLog('bright', `\n📦 CSS 文件:`);
  let totalCSSSize = 0;
  
  cssFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalCSSSize += size;
    
    colorLog('blue', `  ${file}: ${formatBytes(size)}`);
  });

  // 分析压缩文件
  if (gzFiles.length > 0) {
    colorLog('bright', `\n🗜️  Gzip 压缩文件:`);
    gzFiles.forEach(file => {
      const filePath = path.join(distPath, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      
      colorLog('magenta', `  ${file}: ${formatBytes(size)}`);
    });
  }

  // 总结
  colorLog('bright', '\n📈 构建总结:');
  colorLog('cyan', `  总 JS 大小: ${formatBytes(totalJSSize)}`);
  colorLog('cyan', `  总 CSS 大小: ${formatBytes(totalCSSSize)}`);
  colorLog('cyan', `  总文件数: ${jsFiles.length + cssFiles.length}`);

  // 性能建议
  colorLog('bright', '\n💡 性能建议:');
  
  const mainBundle = jsFiles.find(file => file.startsWith('umi.'));
  if (mainBundle) {
    const mainBundlePath = path.join(distPath, mainBundle);
    const mainBundleSize = fs.statSync(mainBundlePath).size;
    
    if (mainBundleSize > 1024 * 1024) { // > 1MB
      colorLog('yellow', '  ⚠️  主包大小超过 1MB，建议进一步优化');
    } else if (mainBundleSize > 500 * 1024) { // > 500KB
      colorLog('yellow', '  ⚠️  主包大小超过 500KB，可以考虑进一步分割');
    } else {
      colorLog('green', '  ✅ 主包大小合理');
    }
  }

  // 检查是否有过大的chunk
  const largeChunks = jsFiles.filter(file => {
    const filePath = path.join(distPath, file);
    const size = fs.statSync(filePath).size;
    return size > 500 * 1024; // > 500KB
  });

  if (largeChunks.length > 0) {
    colorLog('yellow', `  ⚠️  发现 ${largeChunks.length} 个大于 500KB 的文件，建议检查是否可以进一步分割`);
  }

  // 检查gzip压缩效果
  const mainBundleGz = gzFiles.find(file => file.startsWith('umi.') && file.endsWith('.gz'));
  if (mainBundle && mainBundleGz) {
    const originalSize = fs.statSync(path.join(distPath, mainBundle)).size;
    const compressedSize = fs.statSync(path.join(distPath, mainBundleGz)).size;
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
    
    colorLog('green', `  ✅ Gzip 压缩率: ${compressionRatio}%`);
  }
}

function main() {
  colorLog('bright', '🚀 HChatData 前端构建分析工具\n');
  
  // 检查是否需要构建
  const shouldBuild = process.argv.includes('--build');
  
  if (shouldBuild) {
    colorLog('cyan', '🔨 开始构建...');
    try {
      execSync('npm run build', { stdio: 'inherit' });
      colorLog('green', '✅ 构建完成\n');
    } catch (error) {
      colorLog('red', '❌ 构建失败');
      process.exit(1);
    }
  }
  
  analyzeDistFiles();
  
  colorLog('bright', '\n🎯 优化建议:');
  colorLog('cyan', '  1. 确保大型第三方库已分离到独立的 vendor chunk');
  colorLog('cyan', '  2. 使用路由级别的代码分割');
  colorLog('cyan', '  3. 启用 gzip 压缩');
  colorLog('cyan', '  4. 移除未使用的代码和依赖');
  colorLog('cyan', '  5. 考虑使用 CDN 加载大型第三方库');
}

if (require.main === module) {
  main();
}

module.exports = { analyzeDistFiles, formatBytes };
