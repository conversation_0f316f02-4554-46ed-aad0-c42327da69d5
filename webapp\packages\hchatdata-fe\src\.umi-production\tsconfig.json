{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "moduleResolution": "node", "importHelpers": false, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "../../", "strict": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"], "@umijs/max": ["../../../../node_modules/.pnpm/umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@18.3.1_eslint@8.35.0_jest@27._d5pvzqomnp5az5zle5uryp6pkq/node_modules/umi"], "@umijs/max/typings": ["src/.umi/typings"]}}, "include": ["../../.umirc.ts", "../../.umirc.*.ts", "../../**/*.d.ts", "../../**/*.ts", "../../**/*.tsx"]}