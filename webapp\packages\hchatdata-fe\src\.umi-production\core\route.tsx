// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/chat/mobile","name":"chat","hideInMenu":true,"layout":false,"envEnableList":["chat"],"id":"1"},"2":{"path":"/chat/external","name":"chat","hideInMenu":true,"layout":false,"envEnableList":["chat"],"id":"2"},"3":{"path":"/chat","name":"chat","envEnableList":["chat"],"parentId":"ant-design-pro-layout","id":"3"},"4":{"path":"/agent","name":"agent","envEnableList":["chat"],"access":"ADMIN","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/plugin","name":"plugin","envEnableList":["chat"],"access":"ADMIN","parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/model/metric/edit/:metricId","name":"metricEdit","hideInMenu":true,"envEnableList":["semantic"],"parentId":"ant-design-pro-layout","id":"6"},"7":{"path":"/model/","name":"semanticModel","envEnableList":["semantic"],"access":"ADMIN","parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/model/","redirect":"/model/domain","parentId":"7","id":"8"},"9":{"path":"/model/domain/","access":"ADMIN","parentId":"7","id":"9"},"10":{"path":"/model/domain/:domainId","parentId":"9","id":"10"},"11":{"path":"/model/domain/:domainId/:menuKey","parentId":"10","id":"11"},"12":{"path":"/model/domain/manager/:domainId/:modelId","parentId":"9","id":"12"},"13":{"path":"/model/domain/manager/:domainId/:modelId/:menuKey","parentId":"12","id":"13"},"14":{"path":"/model/dataset/:domainId/:datasetId","envEnableList":["semantic"],"parentId":"7","id":"14"},"15":{"path":"/model/dataset/:domainId/:datasetId/:menuKey","parentId":"14","id":"15"},"16":{"path":"/model/metric/:domainId/:modelId/:metricId","envEnableList":["semantic"],"parentId":"7","id":"16"},"17":{"path":"/model/dimension/:domainId/:modelId/:dimensionId","envEnableList":["semantic"],"parentId":"7","id":"17"},"18":{"path":"/metric","name":"metric","envEnableList":["semantic"],"access":"ADMIN","parentId":"ant-design-pro-layout","id":"18"},"19":{"path":"/metric","redirect":"/metric/market","parentId":"18","id":"19"},"20":{"path":"/metric/market","access":"ADMIN","hideInMenu":true,"envEnableList":["semantic"],"parentId":"18","id":"20"},"21":{"path":"/metric/detail/:metricId","name":"metricDetail","hideInMenu":true,"envEnableList":["semantic"],"parentId":"18","id":"21"},"22":{"path":"/metric/detail/edit/:metricId","name":"metricDetail","hideInMenu":true,"envEnableList":["semantic"],"parentId":"18","id":"22"},"23":{"path":"/tag","name":"tag","envEnableList":["semantic"],"hideInMenu":true,"parentId":"ant-design-pro-layout","id":"23"},"24":{"path":"/tag","redirect":"/tag/market","parentId":"23","id":"24"},"25":{"path":"/tag/market","hideInMenu":true,"envEnableList":["semantic"],"parentId":"23","id":"25"},"26":{"path":"/tag/detail/:tagId","name":"tagDetail","hideInMenu":true,"envEnableList":["semantic"],"parentId":"23","id":"26"},"27":{"path":"/login","name":"login","layout":false,"hideInMenu":true,"id":"27"},"28":{"path":"/database","name":"database","envEnableList":["semantic"],"access":"ADMIN","parentId":"ant-design-pro-layout","id":"28"},"29":{"path":"/llm","name":"llm","envEnableList":["semantic"],"access":"ADMIN","parentId":"ant-design-pro-layout","id":"29"},"30":{"path":"/system","name":"system","access":"SYSTEM_ADMIN","parentId":"ant-design-pro-layout","id":"30"},"31":{"path":"/user","name":"user","access":"ADMIN","parentId":"ant-design-pro-layout","id":"31"},"32":{"path":"/","redirect":"/chat","parentId":"ant-design-pro-layout","id":"32"},"33":{"path":"/401","parentId":"ant-design-pro-layout","id":"33"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__ChatPage__index" */'@/pages/ChatPage/index.tsx')),
'2': React.lazy(() => import(/* webpackChunkName: "p__ChatPage__index" */'@/pages/ChatPage/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__ChatPage__index" */'@/pages/ChatPage/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__Agent__index" */'@/pages/Agent/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__ChatPlugin__index" */'@/pages/ChatPlugin/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Metric__Edit" */'@/pages/SemanticModel/Metric/Edit.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__index" */'@/pages/SemanticModel/index.tsx')),
'8': React.lazy(() => import('./EmptyRoute')),
'9': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__OverviewContainer" */'@/pages/SemanticModel/OverviewContainer.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__DomainManager" */'@/pages/SemanticModel/DomainManager.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__DomainManager" */'@/pages/SemanticModel/DomainManager.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__ModelManager" */'@/pages/SemanticModel/ModelManager.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__ModelManager" */'@/pages/SemanticModel/ModelManager.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__View__components__Detail" */'@/pages/SemanticModel/View/components/Detail.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__View__components__Detail" */'@/pages/SemanticModel/View/components/Detail.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Metric__Edit" */'@/pages/SemanticModel/Metric/Edit.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Dimension__Detail" */'@/pages/SemanticModel/Dimension/Detail.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Metric__index" */'@/pages/SemanticModel/Metric/index.tsx')),
'19': React.lazy(() => import('./EmptyRoute')),
'20': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Metric__Market" */'@/pages/SemanticModel/Metric/Market.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Metric__Detail" */'@/pages/SemanticModel/Metric/Detail.tsx')),
'22': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Metric__Edit" */'@/pages/SemanticModel/Metric/Edit.tsx')),
'23': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Insights__index" */'@/pages/SemanticModel/Insights/index.tsx')),
'24': React.lazy(() => import('./EmptyRoute')),
'25': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Insights__Market" */'@/pages/SemanticModel/Insights/Market.tsx')),
'26': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__Insights__Detail" */'@/pages/SemanticModel/Insights/Detail.tsx')),
'27': React.lazy(() => import(/* webpackChunkName: "p__Login__index" */'@/pages/Login/index.tsx')),
'28': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__components__Database__DatabaseTable" */'@/pages/SemanticModel/components/Database/DatabaseTable.tsx')),
'29': React.lazy(() => import(/* webpackChunkName: "p__SemanticModel__components__LLM__LlmTable" */'@/pages/SemanticModel/components/LLM/LlmTable.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__System__index" */'@/pages/System/index.tsx')),
'31': React.lazy(() => import(/* webpackChunkName: "p__User__UserTable" */'@/pages/User/UserTable.tsx')),
'32': React.lazy(() => import('./EmptyRoute')),
'33': React.lazy(() => import(/* webpackChunkName: "p__401" */'@/pages/401.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "t__plugin-layout__Layout" */'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-layout/Layout.tsx')),
},
  };
}
