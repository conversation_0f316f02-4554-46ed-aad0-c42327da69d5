package com.suite.chatdatabi.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.suite.chatdatabi.agent.llm.LLMSettings;
import com.suite.chatdatabi.headless.server.persistence.dataobject.YamlDO;
import com.suite.chatdatabi.headless.server.service.impl.YamlValueServiceImpl;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Data
@Configuration
public class GenieConfig {

    @Autowired
    private ResourceLoader resourceLoader;
    @Autowired
    YamlValueServiceImpl yamlValueService;

    // 获取当前环境 两种：db/yaml
    @Value("${spring.profiles.prompt.type}")
    private String promptType;

    @Value("${spring.profiles.prompt.active}")
    private String promptActive;

    private Map<String, String> plannerSystemPromptMap = new HashMap<>();

    private Map<String, String> plannerNextStepPromptMap = new HashMap<>();

    private Map<String, String> executorSystemPromptMap = new HashMap<>();

    private Map<String, String> executorNextStepPromptMap = new HashMap<>();

    private Map<String, String> executorSopPromptMap = new HashMap<>();

    private Map<String, String> reactSystemPromptMap = new HashMap<>();

    private Map<String, String> reactNextStepPromptMap = new HashMap<>();

    private String plannerModelName;

    private String executorModelName;

    private String reactModelName;

    private String planToolDesc;

    private String codeAgentDesc;

    private String reportToolDesc;

    private String fileToolDesc;

    private String deepSearchToolDesc;

    /**
     * planTool 配置
     */
    private Map<String, Object> planToolParams = new HashMap<>();

    /**
     * codeAgent 配置
     */
    private Map<String, Object> codeAgentPamras = new HashMap<>();

    /**
     * reportTool 配置
     */
    private Map<String, Object> reportToolPamras = new HashMap<>();

    /**
     * fileTool 配置
     */
    private Map<String, Object> fileToolPamras = new HashMap<>();

    /**
     * DeepSearchTool 配置
     */
    private Map<String, Object> deepSearchToolPamras = new HashMap<>();


    private Integer fileToolContentTruncateLen;

    private Integer deepSearchToolFileDescTruncateLen;

    private Integer deepSearchToolMessageTruncateLen;

    private String planPrePrompt;

    private String taskPrePrompt;

    private String clearToolMessage;

    private String planningCloseUpdate;

    private String deepSearchPageCount;

    private Map<String, String> multiAgentToolListMap = new HashMap<>();


    /**
     * LLM Settings
     */
    private Map<String, LLMSettings> llmSettingsMap;


    private Integer plannerMaxSteps;

    private Integer executorMaxSteps;

    private Integer reactMaxSteps;;

    private String maxObserve;

    private String CodeInterpreterUrl;

    private String DeepSearchUrl;

    private String mcpClientUrl;

    private String[] mcpServerUrlArr;

    private String summarySystemPrompt;

    private String digitalEmployeePrompt;

    private Integer messageSizeLimit;

    private Map<String, String> sensitivePatterns = new HashMap<>();

    private Map<String, String> outputStylePrompts = new HashMap<>();

    private Map<String, String> messageInterval = new HashMap<>();


    private String structParseToolSystemPrompt = "";

	private Integer sseClientReadTimeout;

	private Integer sseClientConnectTimeout;

	private String genieSopPrompt;

    private String genieBasePrompt;

    private String taskCompleteDesc;



    /**
     * 将YAML格式的字符串解析为Properties对象
     * @param yamlStr YAML格式的字符串
     * @return Properties对象
     */
    private static Properties parseYamlStringToProperties(String yamlStr) {
        try {
            // 使用ByteArrayResource将YAML字符串转换为资源
            Resource resource = new ByteArrayResource(yamlStr.getBytes(StandardCharsets.UTF_8));

            // 使用YamlPropertiesFactoryBean解析YAML
            YamlPropertiesFactoryBean yamlFactory = new YamlPropertiesFactoryBean();
            yamlFactory.setResources(resource);
            return yamlFactory.getObject();
        } catch (Exception e) {
            log.error("Failed to parse YAML string to properties: " + e.getMessage(), e);
            return new Properties();
        }
    }

    private Properties getPropertiesResource() {
        ClassLoader classLoader = ClassUtils.getDefaultClassLoader();
        ClassPathResource resource = new ClassPathResource("prompt-config-" + promptActive + ".yaml", classLoader);
        YamlPropertiesFactoryBean yamlFactory = new YamlPropertiesFactoryBean();
        yamlFactory.setResources(resource);
        return yamlFactory.getObject();
    }

    private Properties getDataBaseResource() {
        YamlDO yamlById = yamlValueService.getYamlById(1L);
        String configStr = yamlById.getYamlValue();
        return parseYamlStringToProperties(configStr);
    }

    @PostConstruct
    public void init(){

        log.info("正在从:{} 中加载配置文件", promptType);
        Properties properties;
        if (PromptType.DB.toString().equalsIgnoreCase(promptType)){
             properties = getDataBaseResource();
        }else {
            properties = getPropertiesResource();
            log.info("已加载的配置文件:{}","prompt-config-" + promptActive + ".yaml");
        }

        String llmSettings = properties.getProperty("llm.settings");
        this.llmSettingsMap = JSON.parseObject(llmSettings, new TypeReference<Map<String, LLMSettings>>() {
        });
        // 设置 plannerSystemPromptMap
        String plannerSystemPrompt = properties.getProperty("autobots.autoagent.planner.system_prompt");
        if (plannerSystemPrompt != null) {
            plannerSystemPromptMap = JSONObject.parseObject(plannerSystemPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置 plannerNextStepPromptMap
        String plannerNextStepPrompt = properties.getProperty("autobots.autoagent.planner.next_step_prompt");
        if (plannerNextStepPrompt != null) {
            plannerNextStepPromptMap = JSONObject.parseObject(plannerNextStepPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置 executorSystemPromptMap
        String executorSystemPrompt = properties.getProperty("autobots.autoagent.executor.system_prompt");
        if (executorSystemPrompt != null) {
            executorSystemPromptMap = JSONObject.parseObject(executorSystemPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置 executorNextStepPromptMap
        String executorNextStepPrompt = properties.getProperty("autobots.autoagent.executor.next_step_prompt");
        if (executorNextStepPrompt != null) {
            executorNextStepPromptMap = JSONObject.parseObject(executorNextStepPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置 executorSopPromptMap
        String executorSopPrompt = properties.getProperty("autobots.autoagent.executor.sop_prompt");
        if (executorSopPrompt != null) {
            executorSopPromptMap = JSONObject.parseObject(executorSopPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置 reactSystemPromptMap
        String reactSystemPrompt = properties.getProperty("autobots.autoagent.react.system_prompt");
        if (reactSystemPrompt != null) {
            reactSystemPromptMap = JSONObject.parseObject(reactSystemPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置 reactNextStepPromptMap
        String reactNextStepPrompt = properties.getProperty("autobots.autoagent.react.next_step_prompt");
        if (reactNextStepPrompt != null) {
            reactNextStepPromptMap = JSONObject.parseObject(reactNextStepPrompt, new TypeReference<Map<String, String>>() {});
        }

        // 设置模型名称
        plannerModelName = properties.getProperty("autobots.autoagent.planner.model_name", "gpt-4o-0806");
        executorModelName = properties.getProperty("autobots.autoagent.executor.model_name", "gpt-4o-0806");
        reactModelName = properties.getProperty("autobots.autoagent.react.model_name", "gpt-4o-0806");

        // 设置工具描述
        planToolDesc = properties.getProperty("autobots.autoagent.tool.plan_tool.desc", "");
        codeAgentDesc = properties.getProperty("autobots.autoagent.tool.code_agent.desc", "");
        reportToolDesc = properties.getProperty("autobots.autoagent.tool.report_tool.desc", "");
        fileToolDesc = properties.getProperty("autobots.autoagent.tool.file_tool.desc", "");
        deepSearchToolDesc = properties.getProperty("autobots.autoagent.tool.deep_search_tool.desc", "");

        // 设置工具参数
        String planToolParamsStr = properties.getProperty("autobots.autoagent.tool.plan_tool.params");
        if (planToolParamsStr != null) {
            planToolParams = JSON.parseObject(planToolParamsStr, Map.class);
        }

        String codeAgentParamsStr = properties.getProperty("autobots.autoagent.tool.code_agent.params");
        if (codeAgentParamsStr != null) {
            codeAgentPamras = JSON.parseObject(codeAgentParamsStr, Map.class);
        }

        String reportToolParamsStr = properties.getProperty("autobots.autoagent.tool.report_tool.params");
        if (reportToolParamsStr != null) {
            reportToolPamras = JSON.parseObject(reportToolParamsStr, Map.class);
        }

        String fileToolParamsStr = properties.getProperty("autobots.autoagent.tool.file_tool.params");
        if (fileToolParamsStr != null) {
            fileToolPamras = JSON.parseObject(fileToolParamsStr, Map.class);
        }

        String deepSearchToolParamsStr = properties.getProperty("autobots.autoagent.tool.deep_search.params");
        if (deepSearchToolParamsStr != null) {
            deepSearchToolPamras = JSON.parseObject(deepSearchToolParamsStr, Map.class);
        }

        // 设置长度限制
        String fileToolContentTruncateLenStr = properties.getProperty("autobots.autoagent.tool.file_tool.truncate_len");
        if (fileToolContentTruncateLenStr != null) {
            fileToolContentTruncateLen = Integer.valueOf(fileToolContentTruncateLenStr);
        }

        String deepSearchToolFileDescTruncateLenStr = properties.getProperty("autobots.autoagent.tool.deep_search.file_desc.truncate_len");
        if (deepSearchToolFileDescTruncateLenStr != null) {
            deepSearchToolFileDescTruncateLen = Integer.valueOf(deepSearchToolFileDescTruncateLenStr);
        }

        String deepSearchToolMessageTruncateLenStr = properties.getProperty("autobots.autoagent.tool.deep_search.message.truncate_len");
        if (deepSearchToolMessageTruncateLenStr != null) {
            deepSearchToolMessageTruncateLen = Integer.valueOf(deepSearchToolMessageTruncateLenStr);
        }

        // 设置提示词
        planPrePrompt = properties.getProperty("autobots.autoagent.planner.pre_prompt", "分析问题并制定计划：");
        taskPrePrompt = properties.getProperty("autobots.autoagent.task.pre_prompt", "参考对话历史回答，");
        clearToolMessage = properties.getProperty("autobots.autoagent.tool.clear_tool_message", "1");
        planningCloseUpdate = properties.getProperty("autobots.autoagent.planner.close_update", "1");
        deepSearchPageCount = properties.getProperty("autobots.autoagent.deep_search_page_count", "5");

        // 设置工具列表
        String multiAgentToolList = properties.getProperty("autobots.autoagent.tool_list");
        if (multiAgentToolList != null) {
            multiAgentToolListMap = JSONObject.parseObject(multiAgentToolList, new TypeReference<Map<String, String>>() {});
        }


        // 设置最大步骤数
        String plannerMaxStepsStr = properties.getProperty("autobots.autoagent.planner.max_steps");
        if (plannerMaxStepsStr != null) {
            plannerMaxSteps = Integer.valueOf(plannerMaxStepsStr);
        }

        String executorMaxStepsStr = properties.getProperty("autobots.autoagent.executor.max_steps");
        if (executorMaxStepsStr != null) {
            executorMaxSteps = Integer.valueOf(executorMaxStepsStr);
        }

        String reactMaxStepsStr = properties.getProperty("autobots.autoagent.react.max_steps");
        if (reactMaxStepsStr != null) {
            reactMaxSteps = Integer.valueOf(reactMaxStepsStr);
        }

        // 设置其他URL
        maxObserve = properties.getProperty("autobots.autoagent.executor.max_observe", "10000");

        CodeInterpreterUrl = properties.getProperty("autobots.autoagent.code_interpreter_url", "");
        DeepSearchUrl = properties.getProperty("autobots.autoagent.deep_search_url", "");
        mcpClientUrl = properties.getProperty("autobots.autoagent.mcp_client_url", "");

        List<String> mcpServerUrlList = new ArrayList<>();
        int index = 0;
        while (true) {
            String key = "autobots.autoagent.mcp_server_url[" + index + "]";
            String url = properties.getProperty(key);
            if (url == null || url.trim().isEmpty()) {
                break;
            }
            mcpServerUrlList.add(url.trim());
            index++;
        }
        mcpServerUrlArr = mcpServerUrlList.toArray(new String[0]);
        // 设置摘要和数字员工提示词
        summarySystemPrompt = properties.getProperty("autobots.autoagent.summary.system_prompt", "");
        digitalEmployeePrompt = properties.getProperty("autobots.autoagent.digital_employee_prompt", "");

        String messageSizeLimitStr = properties.getProperty("autobots.autoagent.summary.message_size_limit");
        if (messageSizeLimitStr != null) {
            messageSizeLimit = Integer.valueOf(messageSizeLimitStr);
        }

        // 设置敏感词模式
        String sensitivePatternsStr = properties.getProperty("autobots.autoagent.sensitive_patterns");
        if (sensitivePatternsStr != null) {
            sensitivePatterns = JSON.parseObject(sensitivePatternsStr, new TypeReference<Map<String, String>>() {});
        }

        // 设置输出样式提示词
        String outputStylePromptsStr = properties.getProperty("autobots.autoagent.output_style_prompts");
        if (outputStylePromptsStr != null) {
            outputStylePrompts = JSON.parseObject(outputStylePromptsStr, new TypeReference<Map<String, String>>() {});
        }

        // 设置消息间隔
        String messageIntervalStr = properties.getProperty("autobots.autoagent.message_interval");
        if (messageIntervalStr != null) {
            messageInterval = JSON.parseObject(messageIntervalStr, new TypeReference<Map<String, String>>() {});
        }

        // 设置结构化解析工具系统提示词
        structParseToolSystemPrompt = properties.getProperty("autobots.autoagent.struct_parse_tool_system_prompt", "");

        // 设置SSE客户端超时时间
        String sseClientReadTimeoutStr = properties.getProperty("autobots.multiagent.sseClient.readTimeout","1800");
        if (sseClientReadTimeoutStr != null) {
            sseClientReadTimeout = Integer.valueOf(sseClientReadTimeoutStr);
        }

        String sseClientConnectTimeoutStr = properties.getProperty("autobots.multiagent.sseClient.connectTimeout","1800");
        if (sseClientConnectTimeoutStr != null) {
            sseClientConnectTimeout = Integer.valueOf(sseClientConnectTimeoutStr);
        }

        // 设置genie提示词
        genieSopPrompt = properties.getProperty("autobots.autoagent.genie_sop_prompt", "");
        genieBasePrompt = properties.getProperty("autobots.autoagent.genie_base_prompt", "");

        // 设置任务完成描述
        taskCompleteDesc = properties.getProperty("autobots.autoagent.tool.task_complete_desc", "当前task完成，请将当前task标记为 completed");
    }

}

