{"name": "hchatdata-fe", "version": "0.1.0", "private": true, "description": "data chat", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "analyze:size": "node scripts/build-analysis.js", "build:analyze": "node scripts/build-analysis.js --build", "build": "npm run build:os", "build:os": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource max build", "build:os-local": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build", "build:inner": "cross-env REACT_APP_ENV=prod APP_TARGET=inner max build", "build:test": "cross-env REACT_APP_ENV=test max build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:osdev", "dev:os": "npm run start:osdev", "dev:inner": "npm run start:dev", "no:dev:os": "NODE_OPTIONS=--openssl-legacy-provider npm run start:osdev", "no:dev:inner": "NODE_OPTIONS=--openssl-legacy-provider npm run start:dev", "no:build:inner": "cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=prod APP_TARGET=inner max build", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "lint": "max g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "start": "npm run start:osdev", "start:dev": "cross-env PORT=8002 REACT_APP_ENV=dev MOCK=none APP_TARGET=inner max dev", "start:osdev": "cross-env REACT_APP_ENV=dev PORT=9000 MOCK=none APP_TARGET=opensource max dev", "start:no-mock": "cross-env MOCK=none max dev", "start:no-ui": "cross-env UMI_UI=none max dev", "start:pre": "cross-env REACT_APP_ENV=pre max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none max dev", "pretest": "node ./tests/beforeTest", "test": "max test", "test:all": "node ./tests/run-tests.js", "test:component": "max test ./src/components", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "2.7.0", "@antv/dom-util": "^2.0.4", "@antv/g6": "^4.8.23", "@antv/g6-core": "^0.8.23", "@antv/layout": "^0.3.20", "@antv/x6": "1.30.1", "@babel/runtime": "^7.22.5", "@types/numeral": "^2.0.2", "@types/react-draft-wysiwyg": "^1.13.2", "@types/react-syntax-highlighter": "^13.5.0", "@umijs/route-utils": "2.2.2", "ace-builds": "^1.4.12", "ahooks": "^3.7.7", "antd": "^5.17.4", "classnames": "^2.2.6", "compression-webpack-plugin": "^11.0.0", "copy-to-clipboard": "^3.3.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.0.2", "echarts-for-react": "^3.0.1", "eslint-config-tencent": "^1.0.4", "hchatdata-chat-sdk": "workspace:*", "jsencrypt": "^3.0.1", "lodash": "^4.17.11", "moment": "^2.29.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "omit.js": "^2.0.2", "path-to-regexp": "^2.4.0", "qs": "^6.9.0", "query-string": "^9.0.0", "react": "^18.3.1", "react-ace": "^9.4.1", "react-dom": "^18.3.1", "react-spinners": "^0.13.8", "react-split-pane": "^2.0.3", "react-syntax-highlighter": "^15.4.3", "sql-formatter": "^15.6.1", "supersonic-insights-flow-components": "^1.4.9", "umi-request": "1.4.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@types/classnames": "^2.2.7", "@types/crypto-js": "^4.0.1", "@types/draftjs-to-html": "^0.8.0", "@types/echarts": "^4.9.4", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/pinyin": "^2.8.3", "@types/qs": "^6.5.3", "@types/react": "18.3.1", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^4.0.1", "@umijs/max": "^4.2.5", "@umijs/plugin-model": "^2.6.2", "carlo": "^0.9.46", "compression": "^1.8.1", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "eslint": "^7.1.0", "eslint-plugin-chalk": "^1.0.0", "eslint-plugin-import": "^2.27.5", "express": "^4.21.2", "gh-pages": "^3.0.0", "http-proxy-middleware": "^2.0.6", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "prettier": "^2.3.1", "pro-download": "1.0.1", "puppeteer-core": "^5.0.0", "stylelint": "^13.0.0", "typescript": "^4.0.3", "umi-presets-pro": "2.0.2"}, "engines": {"node": ">=16.0.0"}, "__npminstall_done": false, "packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee"}