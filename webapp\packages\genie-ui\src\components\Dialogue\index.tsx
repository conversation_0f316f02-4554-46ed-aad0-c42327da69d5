import { FC } from "react";
import AttachmentList from "../AttachmentList";
import LoadingDot from "../LoadingDot";
import LoadingSpinner from "../LoadingSpinner";
import { buildAction, buildAttachment } from "../../utils/chat";
import {
  CheckOutlined,
  SearchOutlined,
  EditOutlined,
  CodeOutlined,
  ToolOutlined,
  RocketOutlined,
  BulbOutlined,
} from '@ant-design/icons';

// 图标映射函数，替换原来的字体图标
const getIconComponent = (messageType: string) => {
  switch (messageType) {
    case 'plan':
      return <RocketOutlined />;
    case 'plan_thought':
      return <BulbOutlined />;
    case 'tool_result':
      return <ToolOutlined />;
    case 'browser':
      return <SearchOutlined />;
    case 'file':
      return <EditOutlined />;
    case 'deep_search':
      return <SearchOutlined />;
    case 'code':
    case 'html':
      return <CodeOutlined />;
    default:
      return <EditOutlined />;
  }
};

type Props = {
  chat: CHAT.ChatItem;
  deepThink: boolean;
  changeTask?: (task: CHAT.Task) => void;
  changeFile?: (file: CHAT.TFile) => void;
  changePlan?: () => void;
};

const PlanSection: FC<{ plan: CHAT.PlanItem[] }> = ({ plan }) => (
  <div>
    <div className="text-[16px] font-[600] mb-[8px]">任务计划</div>
    {plan.map((p, i) => (
      <div key={i} className="mb-[8px]">
        <div className="h-[22px] text-[#2029459E] text-[15px] font-[500] flex items-center mb-[5px]">
          <div className="w-[6px] h-[6px] rounded-[50%] bg-[#27272a] mx-8"></div>
          {p.name}
        </div>
        <div className="ml-[22px] text-[15px]">
          {p.list.map((step, j) => (
            <div key={j} className="leading-[22px]">
              {j + 1}.{step}
            </div>
          ))}
        </div>
      </div>
    ))}
  </div>
);

const ToolItem: FC<{
  tool: CHAT.Task;
  changePlan?: () => void;
  changeActiveChat: (task: CHAT.Task) => void;
  changeFile?: (file: CHAT.TFile) => void;
}> = ({ tool, changePlan, changeActiveChat, changeFile }) => {
  const actionInfo = buildAction(tool);
  switch (tool.messageType) {
    case "plan": {
      const completedIndex = tool.plan?.stepStatus.lastIndexOf("completed") || 0;
      return (
        <div
          className="mt-[8px] flex items-center px-10 py-6 bg-[#F2F3F7] w-fit rounded-[16px] cursor-pointer overflow-hidden  max-w-full"
          onClick={() => changePlan?.()}
        >
          {/* <CheckOutlined /> */}
          ✔️
          <div className="px-8 flex items-center overflow-hidden">
            <div className="shrink-1">已完成</div>
            <div className="text-[#2029459E] text-[13px] flex-1 overflow-hidden whitespace-nowrap text-ellipsis ml-[8px]">
              {tool.plan?.steps[completedIndex]}
            </div>
          </div>
        </div>
      );
    }
    case "tool_thought": {
      return (
        <div className="rounded-[12px] bg-[#F2F3F7] px-12 py-8 mt-[8px]">
          <div className="mb-[4px]">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7505" width="16" height="16"><path d="M511.307093 129.410685c181.026014 0 327.794573 144.591664 327.794573 322.867916 0 124.312168-71.379245 232.297622-175.984783 286.20442v24.23228c0 6.874406-0.572867 13.691524-1.775888 20.222209a55.568112 55.568112 0 0 1 27.497622 48.063553v37.236364c0 29.216224-22.341818 53.16207-50.985175 55.682685l-2.692475 0.17186h-22.513679c0 54.823385-45.199217 99.335161-100.939188 99.335161-55.625399 0-100.824615-44.45449-100.824616-99.335161h-19.248336l-2.234181-0.114573h-3.723637a56.312839 56.312839 0 0 1-49.954014-55.739972v-37.236364c0-20.164923 10.712615-37.809231 26.695609-47.662546a105.923133 105.923133 0 0 1-1.947749-20.623216V738.998601c-105.178406-53.792224-177.015944-162.006825-177.015944-286.72 0-178.276252 146.768559-322.867916 327.851861-322.867916z m45.48565 794.566714H466.795316c0 23.945846 19.935776 43.537902 44.97007 43.537902a44.282629 44.282629 0 0 0 45.027357-43.423329v-0.114573z m75.733035-93.148196h-240.604196l-0.286434 37.236363h241.291637l0.057287-36.94993-0.458294-0.286433zM511.307093 185.379804c-150.377622 0-271.940028 119.671944-271.940028 266.956084 0 100.538182 57.229427 191.165762 146.653986 236.88056l30.304671 15.581986v57.85958a53.84951 53.84951 0 0 0 0.916587 9.738741l0.572867 2.577902h65.994294v-114.115133l-95.095944-117.265902-0.744727-0.973874-1.260308-2.062321-0.401007-0.572868-0.572867-1.203021-1.718601-4.411076-0.802014-4.181931-0.171861-4.926657 0.171861-2.119608 1.203021-5.213091 1.718601-3.952783 1.718601-2.978909 3.265343-3.895497 3.66635-3.208056 1.489454-0.973874 4.582937-2.234182 3.551777-1.145734 4.010069-0.630154 2.520616-0.114573h201.763804c2.176895 0 4.296503 0.286434 6.416112 0.744727l3.83821 1.203021 3.036196 1.374881 4.296503 2.921622 4.067357 4.124644 2.692475 4.01007 2.119609 4.69751 0.916587 3.551776 0.51558 5.327665-0.229146 3.437202-0.802014 4.124644-0.973875 2.749762-1.317594 2.864336-1.145734 1.890461-1.718602 2.520616-94.694937 116.463888v114.115133h66.108867l0.458294-2.062322a56.541986 56.541986 0 0 0 0.916588-10.197035v-58.317874l30.304671-15.581986c88.966266-45.829371 145.680112-136.285091 145.680112-236.536839 0-147.226853-121.505119-266.956084-271.882741-266.956084v0.114573z m42.621314 369.327441H469.602365l42.163021 51.844475 42.163021-51.844475zM155.95761 250.11379l27.039329 15.639273a29.789091 29.789091 0 1 1-29.789091 51.615329l-27.039329-15.581986a29.789091 29.789091 0 1 1 29.789091-51.558042v-0.114574z m753.205706 10.999049a29.789091 29.789091 0 0 1-10.999049 40.673567l-26.982042 15.581986a29.789091 29.789091 0 0 1-29.789091-51.558042l26.924756-15.581986a29.789091 29.789091 0 0 1 40.845426 10.884475zM291.784407 76.592336l15.639273 26.982042a29.789091 29.789091 0 1 1-51.615329 29.789091l-15.639272-26.924756a29.789091 29.789091 0 1 1 51.615328-29.846377z m481.552112-10.884476a29.789091 29.789091 0 0 1 10.884476 40.673567l-15.639273 26.982042a29.789091 29.789091 0 0 1-51.558042-29.789091l15.581986-26.982042a29.789091 29.789091 0 0 1 40.673566-10.884476zM512.22368 0a29.789091 29.789091 0 0 1 29.789091 29.789091v31.163972a29.789091 29.789091 0 1 1-59.578182 0V29.846378a29.789091 29.789091 0 0 1 29.789091-29.789091V0z" fill="#FF964B" p-id="7506"></path></svg>
            <span className="ml-[4px]">思考过程</span>
          </div>
          <div className="text-[#2029459E] text-[13px] leading-[20px]">
            {tool.toolThought}
          </div>
        </div>
      );
    }
    case "browser": {
      return (
        <div className="mt-[8px]">
          {tool.resultMap?.steps
            .filter((s) => s.status !== "completed")
            .map((s, idx) => (
              <div key={idx}>
                {getIconComponent(tool.messageType)}
                <div>
                  <div>{actionInfo.action}</div>
                  <div>{s.goal}</div>
                </div>
              </div>
            ))}
        </div>
      );
    }
    case "task_summary": {
      return (
        <div className="mt-[8px]">
          <div className="mb-[8px]">{tool.resultMap.taskSummary}</div>
          <AttachmentList
            files={buildAttachment(tool.resultMap.fileList!)}
            preview={true}
            review={changeFile}
          />
        </div>
      );
    }
    default: {
      const loadingType = ["html", "markdown"];
      const loading =
        !tool.resultMap?.isFinal &&
        ((tool.messageType === "deep_search" &&
          (tool.resultMap.messageType === "extend" ||
            tool.resultMap.messageType === "report")) ||
          loadingType.includes(tool.messageType));
      return (
        <div
          className="mt-[8px] flex items-center px-10 py-6 bg-[#F2F3F7] w-fit rounded-[16px] cursor-pointer overflow-hidden max-w-full"
          onClick={() => changeActiveChat(tool)}
        >
          {loading ? (
            <LoadingSpinner color="#F2F3F7"/>
          ) : (
            getIconComponent(
              tool.messageType === "deep_search" &&
                tool.resultMap.messageType === "report"
                ? "file"
                : tool.messageType
            )
          )}
          <div className="px-8 flex items-center overflow-hidden">
            <div className="shrink-0">{actionInfo.action}</div>
            <div className="text-[#2029459E] text-[13px] overflow-hidden whitespace-nowrap text-ellipsis flex-1 ml-[8px]">
              {actionInfo.name}
            </div>
          </div>
        </div>
      );
    }
  }
};

const TimeLineContent: FC<{
  tasks: CHAT.Task[];
  isReactType: boolean;
  changeActiveChat: (task: CHAT.Task) => void;
  changePlan?: () => void;
  changeFile?: (file: CHAT.TFile) => void;
}> = ({ tasks, isReactType, changeActiveChat, changePlan, changeFile }) => (
  <>
    {tasks.map((t, i) => (
      <div key={i} className="overflow-hidden">
        {!isReactType ? <div className="font-[500]">{t.task}</div> : null}
        {(t.children || []).map((tool, j) => (
          <div key={j}>
            <ToolItem
              tool={tool}
              changePlan={changePlan}
              changeActiveChat={changeActiveChat}
              changeFile={changeFile}
            />
          </div>
        ))}
      </div>
    ))}
  </>
);

const TimeLine: FC<{
  chat: CHAT.ChatItem;
  isReactType: boolean;
  changeActiveChat: (task: CHAT.Task) => void;
  changePlan?: () => void;
  changeFile?: (file: CHAT.TFile) => void;
}> = ({ chat, isReactType, changeActiveChat, changePlan, changeFile }) => (
  <>
    {chat.tasks.map((t, i) => {
      const lastTask = i === chat.tasks.length - 1;
      return (
        <div className="w-full flex" key={i}>
          {/* 在React类型和非React类型下，时间线的显示方式也不同 */}
          {!isReactType ? (
            <div className="w-[30px] mt-[2px] mb-[8px] relative shrink-0 overflow-hidden">
              {lastTask && chat.loading ? (
                <LoadingSpinner/>
              ) : (
                <i className="font_family icon-yiwanchengtianchong text-[#4040ff] text-[16px] absolute top-[-4px] left-0"></i>
              )}
              <div className="h-full w-[1px] border-dashed border-l-[1px] border-[#e0e0e9] ml-[7px] "></div>
            </div>
          ) : null}
          <div className="flex-1 mb-[8px] overflow-hidden">
            <TimeLineContent
              tasks={t}
              isReactType={isReactType}
              changeActiveChat={changeActiveChat}
              changePlan={changePlan}
              changeFile={changeFile}
            />
          </div>
        </div>
      );
    })}
  </>
);

const ConclusionSection: FC<{
  chat: CHAT.ChatItem;
  changeFile?: (file: CHAT.TFile) => void;
}> = ({ chat, changeFile }) => {
  const summary =
    chat.conclusion?.resultMap?.taskSummary ||
    chat.conclusion?.result ||
    "任务已完成";
  return (
    <div className="mb-[8px]">
      <div className="mb-[8px]">{summary}</div>
      <AttachmentList
        files={buildAttachment(chat.conclusion?.resultMap.fileList || [])}
        preview={true}
        review={changeFile}
      />
    </div>
  );
};

const Dialogue: FC<Props> = (props) => {
  const { chat, deepThink, changeTask, changeFile, changePlan } = props;
  const isReactType = !deepThink; 

  const changeActiveChat = (task: CHAT.Task) => {
    changeTask?.(task);
  };

  return (
    <div className="h-full text-[14px] font-normal flex flex-col text-[#27272a]">
      {(chat.files || []).length ? (
        <div className="w-full mt-[24px] justify-end">
          <AttachmentList files={chat.files} preview={false} />
        </div>
      ) : null}
      {chat.query ? (
        <div className="w-full mt-[24px] flex justify-end">
          <div className="max-w-[80%] bg-[#4040FFB2] text-[#fff] px-12 py-8 rounded-[12px] rounded-tr-[12px] rounded-br-[4px] rounded-bl-[12px] ">
            {chat.query}
          </div>
        </div>
      ) : null}
      {/* 已接收到你的任务，将立即开始处理... */}
      {chat.tip ? (
        <div className="w-full rounded-[12px] mt-[24px]">{chat.tip}</div>
      ) : null}
      {/* 只有在非React类型（即深度思考模式）下才会显示思考过程 */}
      {!isReactType && chat.thought ? (
        <div className="w-full px-12 py-8 bg-[#F2F3F7] rounded-[12px] mt-[24px]">
          <div>{chat.thought}</div>
        </div>
      ) : null}
      {/* 同样只有在非React类型（深度思考模式）下才会显示计划列表 */}
      {!isReactType && chat.planList?.length ? (
        <div className="w-full px-12 py-8 rounded-[12px] mt-[24px] bg-[#F2F3F7]">
          <PlanSection plan={chat.planList} />
        </div>
      ) : null}
      {/* 处理模式的区别
        React类型：直接进行"思考-行动"循环，适合简单任务的快速处理
        深度思考模式：先进行计划制定，然后分步执行，适合复杂任务的深度分析 */}
      {chat.tasks.length ? (
        <div className="w-full mt-[24px]">
          <TimeLine
            chat={chat}
            isReactType={isReactType}
            changeActiveChat={changeActiveChat}
            changePlan={changePlan}
            changeFile={changeFile}
          />
        </div>
      ) : null}
      {chat.conclusion ? (
        <div className="w-full">
          <ConclusionSection chat={chat} changeFile={changeFile} />
        </div>
      ) : null}
      {chat.loading ? <LoadingDot /> : null}
    </div>
  );
};

export default Dialogue;
