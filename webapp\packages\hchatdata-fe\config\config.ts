// https://umijs.org/config/
import { defineConfig } from '@umijs/max';
import path from 'path';
import defaultSettings, { publicPath, basePath } from './defaultSettings';
import proxy from './proxy';
import routes from './routes';
import dayjs from 'dayjs';
const { REACT_APP_ENV = 'dev', RUN_TYPE } = process.env;

import ENV_CONFIG from './envConfig';

export default defineConfig({
  favicons: ['/favicon.ico'],
  define: {
    // 添加这个自定义的环境变量
    // 'process.env.REACT_APP_ENV': process.env.REACT_APP_ENV, // * REACT_APP_ENV 本地开发环境：dev，测试服：test，正式服：prod
    'process.env': {
      ...process.env,
      API_BASE_URL: '/api/semantic/', // 直接在define中挂载裸露的全局变量还需要配置eslint，ts相关配置才能导致在使用中不会飘红，冗余较高，这里挂在进程环境下
      CHAT_API_BASE_URL: '/api/chat/',
      AUTH_API_BASE_URL: '/api/auth/',
      SHOW_TAG: false,
      ...ENV_CONFIG,
    },
  },
  metas: [
    {
      name: 'app_version',
      content: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },
  ],
  /**
   * @name 开启 hash 模式
   * @description 让 build 之后的产物包含 hash 后缀。通常用于增量发布和避免浏览器加载缓存。
   * @doc https://umijs.org/docs/api/config#hash
   */
  hash: true,

  /**
   * @name 兼容性设置
   * @description 设置 ie11 不一定完美兼容，需要检查自己使用的所有依赖
   * @doc https://umijs.org/docs/api/config#targets
   */
  // targets: {
  //   ie: 11,
  // },
  /**
   * @name 路由的配置，不在路由中引入的文件不会编译
   * @description 只支持 path，component，routes，redirect，wrappers，title 的配置
   * @doc https://umijs.org/docs/guides/routes
   */
  // umi routes: https://umijs.org/docs/routing
  routes,
  /**
   * @name 主题的配置
   * @description 虽然叫主题，但是其实只是 less 的变量设置
   * @doc antd的主题设置 https://ant.design/docs/react/customize-theme-cn
   * @doc umi 的theme 配置 https://umijs.org/docs/api/config#theme
   */
  theme: {
    // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
    // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
    'root-entry-name': 'variable',
    // 'primary-color': '#f87653',
    'primary-color': '#cc0000',
  },
  /**
   * @name Dayjs 的国际化配置
   * @description 如果对国际化没有要求，打开之后能减少js的包大小
   * @doc https://umijs.org/docs/api/config#ignoremomentlocale
   */
  ignoreMomentLocale: true,
  /**
   * @name 代理配置
   * @description 可以让你的本地服务器代理到你的服务器上，这样你就可以访问服务器的数据了
   * @see 要注意以下 代理只能在本地开发时使用，build 之后就无法使用了。
   * @doc 代理介绍 https://umijs.org/docs/guides/proxy
   * @doc 代理配置 https://umijs.org/docs/api/config#proxy
   */
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  base: basePath,
  publicPath,
  outputPath: RUN_TYPE === 'local' ? 'hchatdata-webapp' : 'dist',
  /**
   * @name 快速热更新配置
   * @description 一个不错的热更新组件，更新时可以保留 state
   */
  fastRefresh: true,
  //============== 以下都是max的插件配置 ===============
  /**
   * @name 数据流插件
   * @@doc https://umijs.org/docs/max/data-flow
   */
  model: {},
  /**
   * 一个全局的初始数据流，可以用它在插件之间共享数据
   * @description 可以用来存放一些全局的数据，比如用户信息，或者一些全局的状态，全局初始状态在整个 Umi 项目的最开始创建。
   * @doc https://umijs.org/docs/max/data-flow#%E5%85%A8%E5%B1%80%E5%88%9D%E5%A7%8B%E7%8A%B6%E6%80%81
   */
  initialState: {},
  /**
   * @name layout 插件
   * @doc https://umijs.org/docs/max/layout-menu
   */
  title: 'AIData',
  layout: {
    locale: true,
    ...defaultSettings,
  },
  /**
   * @name moment2dayjs 插件
   * @description 将项目中的 Dayjs 替换为 Dayjs
   * @doc https://umijs.org/docs/max/moment2dayjs
   */
  // moment2dayjs: {
  //   preset: 'antd',
  //   plugins: ['duration'],
  // },
  /**
   * @name 国际化插件
   * @doc https://umijs.org/docs/max/i18n
   */
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  /**
   * @name antd 插件
   * @description 内置了 babel import 插件
   * @doc https://umijs.org/docs/max/antd#antd
   */
  antd: {},
  /**
   * @name 网络请求配置
   * @description 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
   * @doc https://umijs.org/docs/max/request
   */
  request: {},
  /**
   * @name 权限插件
   * @description 基于 initialState 的权限插件，必须先打开 initialState
   * @doc https://umijs.org/docs/max/access
   */
  access: {},
  /**
   * @name <head> 中额外的 script
   * @description 配置 <head> 中额外的 script
   */
  headScripts: [
    // 解决首次加载时白屏的问题
    { src: `${publicPath}scripts/loading.js`, async: true },
  ],

  //================ pro 插件配置 =================
  presets: ['umi-presets-pro'],

  /**
   * @name 代码分割配置
   * @description 配置代码分割策略，优化首屏加载性能
   * @doc https://umijs.org/docs/api/config#codesplitting
   */
  codeSplitting: {
    jsStrategy: 'granularChunks', // 使用细粒度分包策略
    jsStrategyOptions: {
      // 第三方库分包配置
      vendors: [
        {
          name: 'react-vendor',
          test: /[\\/]node_modules[\\/](react|react-dom|react-router|react-router-dom)[\\/]/,
          priority: 10,
        },
        {
          name: 'antd-vendor',
          test: /[\\/]node_modules[\\/](antd|@ant-design)[\\/]/,
          priority: 9,
        },
        {
          name: 'charts-vendor',
          test: /[\\/]node_modules[\\/](echarts|@antv)[\\/]/,
          priority: 8,
        },
        {
          name: 'utils-vendor',
          test: /[\\/]node_modules[\\/](lodash|dayjs|moment|crypto-js)[\\/]/,
          priority: 7,
        },
        {
          name: 'common-vendor',
          test: /[\\/]node_modules[\\/]/,
          priority: 5,
          minChunks: 2, // 至少被2个chunk引用才分离
        }
      ]
    }
  },

  /**
   * @name MFSU配置
   * @description 启用模块联邦加速构建
   * @doc https://umijs.org/docs/api/config#mfsu
   */
  mfsu: {
    strategy: 'normal',
    include: [
      'antd',
      '@ant-design/icons',
      'react',
      'react-dom',
      'lodash',
      'dayjs',
      'echarts',
      'echarts-for-react'
    ]
  },

  requestRecord: {},
  exportStatic: {},
  alias: {
    'hchatdata-chat-sdk': path.resolve(__dirname, '../../chat-sdk/src/'),
    // 开发环境指向源码以支持热更新，生产环境指向构建后的文件
    'genie-ui': REACT_APP_ENV === 'dev'
      ? path.resolve(__dirname, '../../genie-ui/src/')
      : path.resolve(__dirname, '../../genie-ui/'),
  },

  // 生产环境优化配置
  ...(process.env.NODE_ENV === 'production' && {
    // 启用 gzip 压缩
    chainWebpack(config: any) {
      // const CompressionPlugin = require('compression-webpack-plugin');
      // config.plugin('compression').use(CompressionPlugin, [
      //   {
      //     algorithm: 'gzip',
      //     test: /\.(js|css|html|svg)$/,
      //     threshold: 8192,
      //     minRatio: 0.8,
      //   },
      // ]);

      // 优化分包策略
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          // React相关库
          react: {
            name: 'react-vendor',
            test: /[\\/]node_modules[\\/](react|react-dom|react-router)[\\/]/,
            priority: 10,
            reuseExistingChunk: true,
          },
          // Ant Design相关库
          antd: {
            name: 'antd-vendor',
            test: /[\\/]node_modules[\\/](antd|@ant-design)[\\/]/,
            priority: 9,
            reuseExistingChunk: true,
          },
          // 图表库
          charts: {
            name: 'charts-vendor',
            test: /[\\/]node_modules[\\/](echarts|@antv)[\\/]/,
            priority: 8,
            reuseExistingChunk: true,
          },
          // 工具库
          utils: {
            name: 'utils-vendor',
            test: /[\\/]node_modules[\\/](lodash|dayjs|moment|crypto-js)[\\/]/,
            priority: 7,
            reuseExistingChunk: true,
          },
          // 其他第三方库
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 5,
            minChunks: 2,
            reuseExistingChunk: true,
          },
        },
      });

      return config;
    },
  }),

  esbuildMinifyIIFE: true,

  // 添加 PostCSS 配置以支持 Tailwind CSS
  extraPostCSSPlugins: [
    require('tailwindcss'),
    require('autoprefixer'),
  ],
});

