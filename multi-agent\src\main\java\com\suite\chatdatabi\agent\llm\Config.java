package com.suite.chatdatabi.agent.llm;


import com.suite.chatdatabi.agent.util.SpringContextHolder;
import com.suite.chatdatabi.config.GenieConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;



/**
 * 配置工具类
 */
@Slf4j
public class Config {
    /**
     * 获取 LLM 配置
     */
    public static LLMSettings getLLMConfig(String modelName) {
        ApplicationContext applicationContext = SpringContextHolder.getApplicationContext();
        GenieConfig genieConfig = applicationContext.getBean(GenieConfig.class);
        return genieConfig.getLlmSettingsMap().get(modelName);
    }
}