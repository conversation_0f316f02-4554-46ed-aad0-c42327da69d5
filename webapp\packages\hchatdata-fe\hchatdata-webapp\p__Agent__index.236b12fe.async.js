(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[1337],{47712:function(be,W,n){"use strict";n.d(W,{Z:function(){return c}});var E=n(95687),a=n(44194),ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},_=ie,ce=n(54183),k=function(r,de){return a.createElement(ce.Z,(0,E.Z)({},r,{ref:de,icon:_}))},U=a.forwardRef(k),c=U},15892:function(be,W,n){"use strict";n.d(W,{Z:function(){return c}});var E=n(95687),a=n(44194),ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z"}}]},name:"tool",theme:"outlined"},_=ie,ce=n(54183),k=function(r,de){return a.createElement(ce.Z,(0,E.Z)({},r,{ref:de,icon:_}))},U=a.forwardRef(k),c=U},51308:function(be,W,n){"use strict";n.d(W,{Z:function(){return U}});var E=n(10947),a=n(2206),ie={title:"title___V3aE9",subTitleContainer:"subTitleContainer___gJqJ1"},_=n(31549),ce=E.Z.Paragraph,k=function(G){var r=G.title,de=G.subTitle,H=G.subTitleEditable,Te=H===void 0?!1:H,X=G.onSubTitleChange;return(0,_.jsxs)(a.Z,{direction:"vertical",size:2,style:{width:"100%"},children:[(0,_.jsx)("div",{className:ie.title,children:r}),(0,_.jsx)("div",{className:ie.subTitleContainer,children:Te?(0,_.jsx)(ce,{editable:{onChange:function(Ce){X==null||X(Ce)}},children:de||"\u6DFB\u52A0\u63CF\u8FF0"}):de&&(0,_.jsx)("span",{style:{fontSize:"12px",color:"#7b809a"},children:de})})]})},U=k},94580:function(be,W,n){"use strict";n.d(W,{c:function(){return E}});var E={layout:"vertical"}},61902:function(be,W,n){"use strict";var E=n(73193),a=n.n(E),ie=n(76711),_=n.n(ie),ce=n(90819),k=n.n(ce),U=n(89933),c=n.n(U),G=n(45332),r=n.n(G),de=n(17258),H=n(45826),Te=n(44194),X=n(79735),sn=n(3306),Ce=n(31549),Ve=function(ye,$){if(!ye)return!1;var R=ye.includes("(");return $==="selectedPerson"?!R:$==="selectedDepartment"?!!R:!0};function t(){var tn=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],ye=arguments.length>1?arguments[1]:void 0,$=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"id";return tn.map(function(R){return{title:R.name,value:R[$],key:R[$],isLeaf:!R.subOrganizations,children:R.subOrganizations?t(R.subOrganizations,ye,$):[],disableCheckbox:Ve(R.displayName,ye),checkable:!Ve(R.displayName,ye),icon:(R.displayName||"").includes("(")&&(0,Ce.jsx)(sn.Z,{size:"small",staffName:R.name})}})}var e=function(ye){var $=ye.type,R=$===void 0?"selectedPerson":$,_n=ye.value,Ne=ye.onChange,We=ye.treeSelectProps,Qe=We===void 0?{}:We,ln=(0,Te.useState)([]),rn=r()(ln,2),we=rn[0],Je=rn[1],s=function(){var le=c()(k()().mark(function I(){var j,me,N,pe;return k()().wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.next=2,(0,X.T)();case 2:if(j=P.sent,me=j.code,N=j.data,me!==200){P.next=9;break}return pe=t(N,R),Je(pe),P.abrupt("return");case 9:case"end":return P.stop()}},I)}));return function(){return le.apply(this,arguments)}}();(0,Te.useEffect)(function(){s()},[]);var O=function le(I,j,me){return I.map(function(N){if(N.key===j){var pe=N.children;return N.children&&!N.children.find(function(ue){var P;return(ue==null?void 0:ue.key)===((P=me[0])===null||P===void 0?void 0:P.key)})&&(pe=[].concat(_()(me),_()(N.children))),a()(a()({},N),{},{children:pe})}return N.children.length!==0?a()(a()({},N),{},{children:le(N.children,j,me)}):N})},M=function(I){var j=I.key,me=function(){var N=c()(k()().mark(function pe(){var ue,P,xe,Ke;return k()().wrap(function(Re){for(;;)switch(Re.prev=Re.next){case 0:return Re.next=2,(0,X.a)(j);case 2:ue=Re.sent,P=ue.code,xe=ue.data,P===200&&(Ke=xe.reduce(function(qe,Xe){var mn=Xe.name,un=Xe.displayName;return mn&&un&&qe.push(a()({key:"".concat(j,"-").concat(Xe.id)},Xe)),qe},[]),setTimeout(function(){Je(function(qe){return O(qe,j,t(Ke,R,"key"))})},300));case 6:case"end":return Re.stop()}},pe)}));return function(){return N.apply(this,arguments)}}();return new Promise(function(N){me().then(function(){N()})})},C=function(I){Ne==null||Ne(I)},v=function(I){var j=I.label,me=function(ue){ue.preventDefault(),ue.stopPropagation()},N=j.split("(")[0];return(0,Ce.jsxs)(de.Z,{onMouseDown:me,closable:!0,onClose:function(){var ue=I.value,P=_n.filter(function(xe){return xe!==ue});Ne==null||Ne(P)},style:{marginRight:3,marginBottom:3},children:[R==="selectedPerson"&&(0,Ce.jsx)(sn.Z,{size:"small",staffName:N}),(0,Ce.jsx)("span",{style:{position:"relative",top:"2px",left:"5px"},children:j})]})};return(0,Ce.jsx)(Ce.Fragment,{children:(0,Ce.jsx)(H.Z,a()({showSearch:!0,style:{width:"100%"},value:_n,loadData:M,dropdownStyle:{maxHeight:800,overflow:"auto"},allowClear:!0,multiple:!0,onChange:C,treeCheckable:!0,treeIcon:!0,treeData:we,tagRender:v,treeNodeFilterProp:"title",listHeight:500,showCheckedStrategy:H.Z.SHOW_PARENT},Qe))})};W.Z=e},79735:function(be,W,n){"use strict";n.d(W,{T:function(){return c},a:function(){return k}});var E=n(90819),a=n.n(E),ie=n(89933),_=n.n(ie),ce=n(20221);function k(r){return U.apply(this,arguments)}function U(){return U=_()(a()().mark(function r(de){return a()().wrap(function(Te){for(;;)switch(Te.prev=Te.next){case 0:return Te.abrupt("return",(0,ce.request)("".concat("/api/auth/","user/getUserByOrg/").concat(de),{method:"GET"}));case 1:case"end":return Te.stop()}},r)})),U.apply(this,arguments)}function c(){return G.apply(this,arguments)}function G(){return G=_()(a()().mark(function r(){return a()().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:return H.abrupt("return",(0,ce.request)("".concat("/api/auth/","user/getOrganizationTree"),{method:"GET"}));case 1:case"end":return H.stop()}},r)})),G.apply(this,arguments)}},70348:function(be,W,n){"use strict";n.d(W,{Z:function(){return X}});var E=n(90819),a=n.n(E),ie=n(89933),_=n.n(ie),ce=n(45332),k=n.n(ce),U=n(44194),c=n(26574),G=n(20221),r={userAvatar:"userAvatar___uI2EW",userText:"userText___VoQwT",selectPerson:"selectPerson___84ixG"},de=n(3306),H=n(31549),Te=function(Ce){var Ve=Ce.placeholder,t=Ce.value,e=Ce.isMultiple,tn=e===void 0?!0:e,ye=Ce.onChange,$=(0,U.useState)([]),R=k()($,2),_n=R[0],Ne=R[1],We=(0,G.useModel)("allUserData"),Qe=We.allUserList,ln=We.MrefreshUserList,rn=function(){var we=_()(a()().mark(function Je(){var s;return a()().wrap(function(M){for(;;)switch(M.prev=M.next){case 0:return M.next=2,ln();case 2:s=M.sent,Ne(s);case 4:case"end":return M.stop()}},Je)}));return function(){return we.apply(this,arguments)}}();return(0,U.useEffect)(function(){Array.isArray(Qe)&&Qe.length>0?Ne(Qe):rn()},[]),(0,H.jsx)(c.default,{value:t,placeholder:Ve!=null?Ve:"\u8BF7\u9009\u62E9\u7528\u6237\u540D",mode:tn?"multiple":void 0,allowClear:!0,showSearch:!0,className:r.selectPerson,onChange:ye,children:_n.map(function(we){return(0,H.jsxs)(c.default.Option,{value:we.name,children:[(0,H.jsx)(de.Z,{size:"small",staffName:we.name}),(0,H.jsx)("span",{className:r.userText,children:we.displayName})]},we.name)})})},X=Te},32128:function(be,W,n){"use strict";n.r(W),n.d(W,{default:function(){return ta}});var E=n(73193),a=n.n(E),ie=n(90819),_=n.n(ie),ce=n(89933),k=n.n(ce),U=n(45332),c=n.n(U),G=n(7477),r=n(44194),de=n(10162),H=n(88732),Te=n(47786),X=n(34284),sn=n(61338),Ce=n(84025),Ve=n.n(Ce),t={agent:"agent___dmpCU",agentsSection:"agentsSection___i7MIG",sectionTitle:"sectionTitle___P_Aj4",agentFormContainer:"agentFormContainer___MMdN0",agentFormTitle:"agentFormTitle___NJfy9",content:"content___vcxyM",searchBar:"searchBar___roTwg",searchControl:"searchControl___ZIiLE",agentsContainer:"agentsContainer___e_Qxg",agentItem:"agentItem___Mg_ki",agentActive:"agentActive___vzQtt",agentIcon:"agentIcon___oPSDb",agentContent:"agentContent___ylUyB",agentNameBar:"agentNameBar___QeZet",agentName:"agentName___UVw91",operateIcons:"operateIcons___vpS9H",operateIcon:"operateIcon___PzCfl",bottomBar:"bottomBar___J7lyO",agentDescription:"agentDescription___F0oto",toggleStatus:"toggleStatus___QQrMD",online:"online___o6jYP",toolsSection:"toolsSection___oI2XV",toolsSectionTitleBar:"toolsSectionTitleBar___Kisgf",backIcon:"backIcon___d8WB7",agentTitle:"agentTitle___yArUG",paramsSection:"paramsSection___to8Ye",filterRow:"filterRow___UKfbE",filterParamName:"filterParamName___hkFbV",filterParamValueField:"filterParamValueField___Niv3a",questionExample:"questionExample___zD_yk",basicInfo:"basicInfo___AcNwa",basicInfoTitle:"basicInfoTitle___Ri3Ya",infoContent:"infoContent___lB6rk",infoItem:"infoItem___Zq9fT",label:"label___gsN2N",toolSection:"toolSection___dYbuC",toolSectionTitleBar:"toolSectionTitleBar___Tq1th",toolSectionTitle:"toolSectionTitle___H4nQn",emptyHolder:"emptyHolder___MAPWg",toolsContent:"toolsContent___mn0Ul",toolItem:"toolItem___pS92H",toolIcon:"toolIcon___u5NQ4",toolContent:"toolContent___Bs3GE",toolTopSection:"toolTopSection___gJYNf",toolType:"toolType___ZXQmZ",toolOperateIcons:"toolOperateIcons___wnk7a",toolOperateIcon:"toolOperateIcon___uJirq",toolDesc:"toolDesc___V_Un4",switchShowTypeLabel:"switchShowTypeLabel___U5yGT",memorySection:"memorySection___glvdb",filterSection:"filterSection___InxxN",filterItem:"filterItem___SL6pI",filterItemTitle:"filterItemTitle___APLXe",filterItemControl:"filterItemControl___qmJXy",reviewComment:"reviewComment___HoSOg",commentPopover:"commentPopover___UJ8fd",search:"search___Cod3I",agentChatModelCell:"agentChatModelCell___NUquk",agentChatModelCellActive:"agentChatModelCellActive___qXYva",permissionSection:"permissionSection___vYnpi"},e=n(31549),tn=function(x){var i=x.agents,d=x.loading,K=x.onSelectAgent,re=x.onDeleteAgent,he=x.onSaveAgent,B=x.onCreatBtnClick,te=(0,r.useState)([]),Ae=c()(te,2),oe=Ae[0],Y=Ae[1];(0,r.useEffect)(function(){Y(i)},[i]);var V=[{title:"\u667A\u80FD\u4F53\u540D\u79F0",dataIndex:"name",key:"name",render:function(b,Q){return(0,e.jsx)("a",{onClick:function(){K(Q)},children:b})}},{title:"\u63CF\u8FF0",dataIndex:"description",key:"description"},{title:"\u72B6\u6001",dataIndex:"status",key:"status",render:function(b,Q){return(0,e.jsxs)("div",{className:t.toggleStatus,children:[b===0?"\u5DF2\u7981\u7528":(0,e.jsx)("span",{className:t.online,children:"\u5DF2\u542F\u7528"}),(0,e.jsx)("span",{onClick:function(D){D.stopPropagation()},children:(0,e.jsx)(H.Z,{size:"small",defaultChecked:b===1,onChange:function(D){he(a()(a()({},Q),{},{status:D?1:0}),!0)}},Q.id)})]})}},{title:"\u66F4\u65B0\u4EBA",dataIndex:"updatedBy",key:"updatedBy"},{title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",key:"updatedAt",render:function(b){return Ve()(b).format("YYYY-MM-DD HH:mm:ss")}},{title:"\u64CD\u4F5C",dataIndex:"x",key:"x",render:function(b,Q){return(0,e.jsxs)("div",{className:t.operateIcons,children:[(0,e.jsx)("a",{onClick:function(){K(Q)},children:"\u7F16\u8F91"}),(0,e.jsx)(Te.Z,{title:"\u786E\u5B9A\u5220\u9664\u5417\uFF1F",onCancel:function(D){D==null||D.stopPropagation()},onConfirm:function(){re(Q.id)},children:(0,e.jsx)("a",{children:"\u5220\u9664"})})]})}}];return(0,e.jsx)("div",{className:t.agentsSection,children:(0,e.jsxs)("div",{className:t.content,children:[(0,e.jsx)("div",{className:t.searchBar,children:(0,e.jsxs)(X.ZP,{type:"primary",onClick:function(){B==null||B()},children:[(0,e.jsx)(de.Z,{}),"\u65B0\u5EFA\u667A\u80FD\u4F53"]})}),(0,e.jsx)(sn.Z,{columns:V,dataSource:oe,loading:d,position:"bottomLeft"})]})})},ye=tn,$=n(94202),R=n(20221);function _n(){return(0,R.request)("/api/chat/agent/getAgentList")}function Ne(m){return(0,R.request)("/api/chat/agent",{method:m!=null&&m.id?"PUT":"POST",data:a()(a()({},m),{},{status:m.status!==void 0?m.status:1})})}function We(m){return(0,R.request)("/api/chat/agent/".concat(m),{method:"DELETE"})}function Qe(){return(0,R.request)("/api/chat/conf/getDomainDataSetTree",{method:"GET"})}function ln(m){return request("/api/semantic/metric/queryMetric",{method:"POST",data:{modelIds:[m],current:1,pageSize:2e3}})}function rn(m){var x=m.agentId,i=m.chatMemoryFilter,d=m.current,K=m.pageSize;return(0,R.request)("/api/chat/memory/pageMemories",{method:"POST",data:a()(a()({},m),{},{chatMemoryFilter:a()({agentId:x},i),current:d,pageSize:K||10,sort:"desc"})})}function we(m){return(0,R.request)("/api/chat/memory/updateMemory",{method:"POST",data:m})}function Je(m){return(0,R.request)("/api/chat/memory/batchDelete",{method:"POST",data:{ids:m}})}function s(){return(0,R.request)("".concat("/api/chat/","agent/getToolTypes"),{method:"GET"})}function O(m){return(0,R.request)("/api/chat/memory/createMemory",{method:"POST",data:m})}var M=n(76711),C=n.n(M),v=n(20263),le=n(15783),I=n(26574),j=n(46504),me=n(45826),N=function(m){return m.NL2SQL_RULE="NL2SQL_RULE",m.NL2SQL_LLM="NL2SQL_LLM",m.PLUGIN="PLUGIN",m.DATASET="DATASET",m}({}),pe=function(m){return m.METRIC="METRIC",m.DETAIL="DETAIL",m}({}),ue=[{label:"\u805A\u5408\u6A21\u5F0F",value:pe.METRIC},{label:"\u660E\u7EC6\u6A21\u5F0F",value:pe.DETAIL}],P=function(m){return m.PENDING="PENDING",m.ENABLED="ENABLED",m.DISABLED="DISABLED",m}({}),xe=function(m){return m.POSITIVE="POSITIVE",m.NEGATIVE="NEGATIVE",m}({}),Ke=n(12820),Ge=n(87411),Re=v.Z.Item,qe=function(x){var i=x.editTool,d=x.onSaveTool,K=x.onCancel,re=(0,r.useState)(),he=c()(re,2),B=he[0],te=he[1],Ae=(0,r.useState)([]),oe=c()(Ae,2),Y=oe[0],V=oe[1],ee=(0,r.useState)(!1),b=c()(ee,2),Q=b[0],w=b[1],D=(0,r.useState)([]),ne=c()(D,2),ke=ne[0],_e=ne[1],Le=(0,r.useState)([]),f=c()(Le,2),J=f[0],A=f[1],y=(0,r.useState)([]),S=c()(y,2),Ee=S[0],Se=S[1],F=(0,r.useState)([]),ae=c()(F,2),ge=ae[0],ve=ae[1],se=v.Z.useForm(),nn=c()(se,1),Pe=nn[0],Ze=function(){var z=k()(_()().mark(function q(){var o,p;return _()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,Qe();case 2:o=u.sent,p=(0,$.Dr)(o.data,function(l){var h;l.title=l.name,l.value=l.type==="DOMAIN"?"DOMAIN_".concat(l.id):l.id,l.checkable=l.type==="DATASET"||l.type==="DOMAIN"&&((h=l.children)===null||h===void 0?void 0:h.length)>0}),V([{title:"\u9ED8\u8BA4",value:-1,type:"DATASET"}].concat(C()(p)));case 5:case"end":return u.stop()}},q)}));return function(){return z.apply(this,arguments)}}(),ze=function(){var z=k()(_()().mark(function q(){var o;return _()().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return g.next=2,(0,Ge.UV)({});case 2:o=g.sent,Se(o.data||[]);case 4:case"end":return g.stop()}},q)}));return function(){return z.apply(this,arguments)}}();(0,r.useEffect)(function(){Ze(),ze(),hn()},[]),(0,r.useEffect)(function(){if(i){var z;Pe.setFieldsValue(a()(a()({},i),{},{plugins:(z=i.plugins)===null||z===void 0?void 0:z[0]})),te(i.type),_e((i.exampleQuestions||[]).map(function(q){return{id:(0,$.Vj)(),question:q}})),A(i.metricOptions||[])}else Pe.resetFields()},[i]);var hn=function(){var z=k()(_()().mark(function q(){var o,p,g,u;return _()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,s();case 2:o=h.sent,p=o.code,g=o.data,p===200&&g?(u=Object.keys(g).map(function(T){return{label:g[T],value:T}}),ve(u)):G.ZP.error("\u83B7\u53D6\u5DE5\u5177\u7C7B\u578B\u5931\u8D25");case 6:case"end":return h.stop()}},q)}));return function(){return z.apply(this,arguments)}}(),kn={labelCol:{span:6},wrapperCol:{span:14}},Ie=function(){var z=k()(_()().mark(function q(){var o;return _()().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return g.next=2,Pe.validateFields();case 2:return o=g.sent,w(!0),g.next=6,d(a()(a()({id:i==null?void 0:i.id},o),{},{exampleQuestions:ke.map(function(u){return u.question}).filter(function(u){return u}),plugins:o.plugins?[o.plugins]:void 0,metricOptions:J.map(function(u){return a()(a()({},u),{},{modelId:o.modelId})})}));case 6:w(!1);case 7:case"end":return g.stop()}},q)}));return function(){return z.apply(this,arguments)}}();return(0,e.jsx)(le.Z,{open:!0,title:i?"\u7F16\u8F91\u5DE5\u5177":"\u65B0\u5EFA\u5DE5\u5177",confirmLoading:Q,width:800,maskClosable:!1,onOk:Ie,onCancel:K,children:(0,e.jsxs)(v.Z,a()(a()({},kn),{},{form:Pe,children:[(0,e.jsx)(Re,{name:"type",label:"\u7C7B\u578B",rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u5DE5\u5177\u7C7B\u578B"}],children:(0,e.jsx)(I.default,{options:ge,placeholder:"\u8BF7\u9009\u62E9\u5DE5\u5177\u7C7B\u578B",onChange:te})}),(0,e.jsx)(Re,{name:"name",label:"\u540D\u79F0",children:(0,e.jsx)(j.default,{placeholder:"\u8BF7\u8F93\u5165\u5DE5\u5177\u540D\u79F0"})}),B&&[N.NL2SQL_RULE,N.NL2SQL_LLM,N.DATASET].includes(B)&&(0,e.jsx)(Re,{name:"dataSetIds",label:"\u6570\u636E\u96C6",children:(0,e.jsx)(me.Z,{treeData:Y,placeholder:"\u8BF7\u9009\u62E9\u6570\u636E\u96C6",multiple:!0,treeCheckable:!0,allowClear:!0})}),B===N.NL2SQL_LLM&&(0,e.jsx)(Re,{name:"exampleQuestions",label:"\u793A\u4F8B\u95EE\u9898",children:(0,e.jsxs)("div",{className:t.paramsSection,children:[ke.map(function(z){var q=z.id,o=z.question;return(0,e.jsxs)("div",{className:t.filterRow,children:[(0,e.jsx)(j.default,{placeholder:"\u793A\u4F8B\u95EE\u9898",value:o,className:t.questionExample,onChange:function(g){z.question=g.target.value,_e(C()(ke))},allowClear:!0}),(0,e.jsx)(Ke.Z,{onClick:function(){_e(ke.filter(function(g){return g.id!==q}))}})]},q)}),(0,e.jsxs)(X.ZP,{onClick:function(){_e([].concat(C()(ke),[{id:(0,$.Vj)()}]))},children:[(0,e.jsx)(de.Z,{}),"\u65B0\u589E\u793A\u4F8B\u95EE\u9898"]})]})}),B===N.PLUGIN&&(0,e.jsx)(Re,{name:"plugins",label:"\u63D2\u4EF6",children:(0,e.jsx)(I.default,{placeholder:"\u8BF7\u9009\u62E9\u63D2\u4EF6",options:Ee.map(function(z){return{label:z.name,value:z.id}}),showSearch:!0,filterOption:function(q,o){var p;return((p=o==null?void 0:o.label)!==null&&p!==void 0?p:"").toLowerCase().includes(q.toLowerCase())},onChange:function(q){var o=Ee.find(function(p){return p.id===q});o&&Pe.setFieldsValue({name:o.name})}})}),B===N.NL2SQL_RULE&&(0,e.jsx)(Re,{name:"queryTypes",label:"\u67E5\u8BE2\u6A21\u5F0F",children:(0,e.jsx)(I.default,{placeholder:"\u8BF7\u9009\u62E9\u67E5\u8BE2\u6A21\u5F0F",options:ue,showSearch:!0,mode:"multiple",filterOption:function(q,o){var p;return((p=o==null?void 0:o.label)!==null&&p!==void 0?p:"").toLowerCase().includes(q.toLowerCase())}})})]}))})},Xe=qe,mn=n(47712),un=n(10154),Me=n.n(un),on=n(2206),Fe=n(16156),yn=n(8887),An=n(49841),Sn=n.n(An),gn={mainTitleMark:"mainTitleMark___AfcIc",mark:"mark____J2IE"},En=function(x){return Sn()(x),(0,e.jsxs)("div",{className:gn.mainTitleMark,children:[(0,e.jsx)("i",{className:gn.mark}),(0,e.jsx)("i",{className:gn.mark}),(0,e.jsx)("i",{className:gn.mark})]})},bn=En,Tn=n(15892),Pn=n(4711),Rn=n(31063),Dn=function(x){var i,d=x.currentAgent,K=x.onSaveAgent,re=(0,r.useState)(!1),he=c()(re,2),B=he[0],te=he[1],Ae=(0,r.useState)(),oe=c()(Ae,2),Y=oe[0],V=oe[1],ee=(0,r.useState)([]),b=c()(ee,2),Q=b[0],w=b[1];(0,r.useEffect)(function(){ke()},[]);var D=d!=null&&d.toolConfig?JSON.parse(d.toolConfig):{},ne=function(){var f=k()(_()().mark(function J(A){return _()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.next=2,K(A);case 2:te(!1);case 3:case"end":return S.stop()}},J)}));return function(A){return f.apply(this,arguments)}}(),ke=function(){var f=k()(_()().mark(function J(){var A,y,S,Ee;return _()().wrap(function(F){for(;;)switch(F.prev=F.next){case 0:return F.next=2,s();case 2:A=F.sent,y=A.code,S=A.data,y===200&&S?(Ee=Object.keys(S).map(function(ae){return{label:S[ae],value:ae}}),w(Ee)):G.ZP.error("\u83B7\u53D6\u5DE5\u5177\u7C7B\u578B\u5931\u8D25");case 6:case"end":return F.stop()}},J)}));return function(){return f.apply(this,arguments)}}(),_e=function(){var f=k()(_()().mark(function J(A){var y,S;return _()().wrap(function(Se){for(;;)switch(Se.prev=Se.next){case 0:return y=D||{},y.tools||(y.tools=[]),A.id?(S=y.tools.findIndex(function(F){return F.id===A.id}),y.tools[S]=A):y.tools.push(a()(a()({},A),{},{id:(0,$.Vj)()})),Se.next=5,ne(a()(a()({},d),{},{toolConfig:JSON.stringify(y)}));case 5:te(!1);case 6:case"end":return Se.stop()}},J)}));return function(A){return f.apply(this,arguments)}}(),Le=function(){var f=k()(_()().mark(function J(A){var y;return _()().wrap(function(Ee){for(;;)switch(Ee.prev=Ee.next){case 0:return y=D||{},y.tools||(y.tools=[]),y.tools=y.tools.filter(function(Se){return Se.id!==A.id}),Ee.next=5,ne(a()(a()({},d),{},{toolConfig:JSON.stringify(y)}));case 5:case"end":return Ee.stop()}},J)}));return function(A){return f.apply(this,arguments)}}();return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{className:t.toolSection,children:D!=null&&D.tools&&(D==null||(i=D.tools)===null||i===void 0?void 0:i.length)>0?(0,e.jsx)("div",{className:t.toolsContent,children:D.tools.map(function(f){var J,A=(J=Q.find(function(y){return y.value===f.type}))===null||J===void 0?void 0:J.label;return(0,e.jsxs)("div",{className:t.toolItem,onClick:function(){V(f),te(!0)},children:[(0,e.jsx)(Tn.Z,{className:t.toolIcon}),(0,e.jsxs)("div",{className:t.toolContent,children:[(0,e.jsxs)("div",{className:t.toolTopSection,children:[(0,e.jsx)("div",{className:t.toolType,children:f.name||A}),(0,e.jsxs)("div",{className:t.toolOperateIcons,children:[(0,e.jsx)(Pn.Z,{className:t.toolOperateIcon,onClick:function(S){S.stopPropagation(),V(f),te(!0)}}),(0,e.jsx)(Te.Z,{title:"\u786E\u5B9A\u5220\u9664\u5417\uFF1F",onCancel:function(S){S==null||S.stopPropagation()},onConfirm:function(S){S==null||S.stopPropagation(),Le(f)},children:(0,e.jsx)(Ke.Z,{className:t.toolOperateIcon,onClick:function(S){S.stopPropagation()}})})]})]}),(0,e.jsx)("div",{className:t.toolDesc,title:A,children:A})]})]},f.id)})}):(0,e.jsx)("div",{className:t.emptyHolder,children:(0,e.jsx)(Rn.Z,{description:"".concat(d!=null&&d.name?"\u3010".concat(d==null?void 0:d.name,"\u3011"):"","\u6682\u65E0\u5DE5\u5177\uFF0C\u8BF7\u65B0\u589E\u5DE5\u5177")})})}),B&&(0,e.jsx)(Xe,{editTool:Y,onSaveTool:_e,onCancel:function(){te(!1)}})]})},pn=Dn,On=n(98121),en=n(32693),cn=n(70348),vn=n(4845),Vn=n(82672),Cn=n(34044),wn=n(15154),In=n(24554),Wn=n(94580),xn=v.Z.Item,Mn=j.default.TextArea,Gn=function(x){var i=x.onCancel,d=x.agentId,K=x.open,re=x.onSubmit,he=v.Z.useForm(),B=c()(he,1),te=B[0],Ae=(0,r.useState)(!1),oe=c()(Ae,2),Y=oe[0],V=oe[1],ee=function(){var Q=k()(_()().mark(function w(D){var ne,ke,_e;return _()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return V(!0),f.next=3,O(a()(a()({},D),{},{agentId:d}));case 3:ne=f.sent,ke=ne.code,_e=ne.msg,V(!1),ke===200?re==null||re():G.ZP.error(_e);case 8:case"end":return f.stop()}},w)}));return function(D){return Q.apply(this,arguments)}}(),b=function(){return(0,e.jsx)(e.Fragment,{children:(0,e.jsxs)(on.Z,{children:[(0,e.jsx)(X.ZP,{onClick:function(){i==null||i()},children:"\u53D6\u6D88"}),(0,e.jsx)(X.ZP,{type:"primary",loading:Y,onClick:function(){var D=te.getFieldsValue();ee(D)},children:"\u4FDD \u5B58"})]})})};return(0,e.jsx)(le.Z,{width:600,destroyOnHidden:!0,title:"\u8BB0\u5FC6\u8BBE\u7F6E",style:{top:48},maskClosable:!1,open:K,footer:b(),onCancel:i,children:(0,e.jsxs)(v.Z,a()(a()({},Wn.c),{},{form:te,layout:"vertical",onValuesChange:function(w){},initialValues:{status:P.PENDING},children:[(0,e.jsx)(xn,{name:"question",label:"\u7528\u6237\u95EE\u9898",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u95EE\u9898"}],children:(0,e.jsx)(j.default,{placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u95EE\u9898"})}),(0,e.jsx)(xn,{name:"dbSchema",label:"Schema\u6620\u5C04",children:(0,e.jsx)(Mn,{placeholder:"\u8BF7\u8F93\u5165Schema\u6620\u5C04",style:{height:100}})}),(0,e.jsx)(xn,{name:"s2sql",label:"\u8BED\u4E49S2SQL",children:(0,e.jsx)(Mn,{placeholder:"\u8BF7\u8F93\u5165\u8BED\u4E49S2SQL",style:{height:100}})}),(0,e.jsx)(xn,{name:"status",label:"\u72B6\u6001",children:(0,e.jsxs)(Cn.ZP.Group,{size:"small",buttonStyle:"solid",children:[(0,e.jsx)(Cn.ZP.Button,{value:P.PENDING,children:"\u5F85\u5B9A"}),(0,e.jsx)(Cn.ZP.Button,{value:P.ENABLED,children:"\u5DF2\u542F\u7528"}),(0,e.jsx)(Cn.ZP.Button,{value:P.DISABLED,children:"\u5DF2\u7981\u7528"})]})})]}))})},zn=Gn,qn=n(28977),Fn=n.n(qn),jn=j.default.TextArea,Zn=Cn.ZP.Group,$n=function(x){var i=x.agentId,d=(0,r.useState)([]),K=c()(d,2),re=K[0],he=K[1],B=(0,r.useState)([]),te=c()(B,2),Ae=te[0],oe=te[1],Y=(0,r.useState)(!1),V=c()(Y,2),ee=V[0],b=V[1],Q=(0,r.useState)({}),w=c()(Q,2),D=w[0],ne=w[1],ke=(0,r.useState)(!1),_e=c()(ke,2),Le=_e[0],f=_e[1],J=D.question,A=D.status,y=D.llmReviewRet,S=D.humanReviewRet,Ee=(0,r.useState)([]),Se=c()(Ee,2),F=Se[0],ae=Se[1],ge={current:1,pageSize:10,total:0},ve=(0,r.useState)(ge),se=c()(ve,2),nn=se[0],Pe=se[1],Ze=function(){var o=k()(_()().mark(function p(g){var u,l,h;return _()().wrap(function(Z){for(;;)switch(Z.prev=Z.next){case 0:return Z.next=2,Je(g);case 2:u=Z.sent,l=u.code,h=u.msg,l===200?(Ie(),ae([])):G.ZP.error(h);case 6:case"end":return Z.stop()}},p)}));return function(g){return o.apply(this,arguments)}}(),ze=[{title:"\u7528\u6237\u95EE\u9898",dataIndex:"question",readonly:!0},{title:"Schema\u6620\u5C04",dataIndex:"dbSchema",width:220,valueType:"textarea",renderFormItem:function(p,g){var u=g.record;return(0,e.jsx)(jn,{rows:3,disabled:(u==null?void 0:u.status)===P.ENABLED})}},{title:"\u8BED\u4E49S2SQL",dataIndex:"s2sql",width:220,valueType:"textarea",renderFormItem:function(p,g){var u=g.record;return(0,e.jsx)(jn,{rows:3,disabled:(u==null?void 0:u.status)===P.ENABLED})}},{title:"\u5927\u6A21\u578B\u8BC4\u4F30\u610F\u89C1",dataIndex:"llmReviewCmt",readonly:!0,width:200,render:function(p){return(0,e.jsx)(wn.Z,{trigger:"hover",content:(0,e.jsx)("div",{className:t.commentPopover,children:p}),children:(0,e.jsx)("div",{className:t.reviewComment,children:p})})}},{title:"\u5927\u6A21\u578B\u8BC4\u4F30\u7ED3\u679C",key:"llmReviewRet",dataIndex:"llmReviewRet",readonly:!0,width:150,valueEnum:Me()(Me()({},xe.POSITIVE,{text:"\u6B63\u786E",status:"Success"}),xe.NEGATIVE,{text:"\u9519\u8BEF",status:"Error"}),sorter:!0},{title:"\u7BA1\u7406\u5458\u8BC4\u4F30\u610F\u89C1",dataIndex:"humanReviewCmt",valueType:"textarea",renderFormItem:function(p,g){var u=g.record;return(0,e.jsx)(jn,{rows:12,disabled:(u==null?void 0:u.status)===P.ENABLED})},render:function(p){return p==="-"?"-":(0,e.jsx)(wn.Z,{trigger:"hover",content:(0,e.jsx)("div",{className:t.commentPopover,children:p}),children:(0,e.jsx)("div",{className:t.reviewComment,children:p})})}},{title:"\u7BA1\u7406\u5458\u8BC4\u4F30\u7ED3\u679C",key:"humanReviewRet",dataIndex:"humanReviewRet",width:150,sorter:!0,valueType:"radio",renderFormItem:function(p,g){var u=g.record;return(0,e.jsx)(Zn,{disabled:(u==null?void 0:u.status)===P.ENABLED,options:[{label:"\u6B63\u786E",value:xe.POSITIVE},{label:"\u9519\u8BEF",value:xe.NEGATIVE}]})},valueEnum:Me()(Me()({},xe.POSITIVE,{text:"\u6B63\u786E",status:"Success"}),xe.NEGATIVE,{text:"\u9519\u8BEF",status:"Error"})},{title:"\u72B6\u6001",key:"status",dataIndex:"status",valueType:"radio",width:120,sorter:!0,tooltip:"\u82E5\u542F\u7528\uFF0C\u5C06\u4F1A\u628A\u8FD9\u6761\u8BB0\u5F55\u52A0\u5165\u5230\u5411\u91CF\u5E93\u4E2D\u4F5C\u4E3A\u6837\u4F8B\u53EC\u56DE\u4F9B\u5927\u6A21\u578B\u53C2\u8003\u4EE5\u53CA\u4F5C\u4E3A\u76F8\u4F3C\u95EE\u9898\u63A8\u8350\u7ED9\u7528\u6237",valueEnum:Me()(Me()(Me()({},P.PENDING,{text:"\u5F85\u5B9A"}),P.ENABLED,{text:"\u542F\u7528"}),P.DISABLED,{text:"\u7981\u7528"}),render:function(p,g){var u=g.status;return u===P.PENDING?(0,e.jsx)(In.Z,{status:"default",text:"\u5F85\u5B9A"}):u===P.ENABLED?(0,e.jsx)(In.Z,{status:"success",text:"\u5DF2\u542F\u7528"}):(0,e.jsx)(In.Z,{status:"error",text:"\u5DF2\u7981\u7528"})}},{dataIndex:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",editable:!1,search:!1,sorter:!0,render:function(p){return p&&p!=="-"?Fn()(p).format("YYYY-MM-DD HH:mm:ss"):"-"}},{dataIndex:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",search:!1,editable:!1,sorter:!0,render:function(p){return p&&p!=="-"?Fn()(p).format("YYYY-MM-DD HH:mm:ss"):"-"}},{title:"\u64CD\u4F5C",valueType:"option",width:150,render:function(p,g,u,l){return[(0,e.jsx)("a",{onClick:function(){var T;l==null||(T=l.startEditable)===null||T===void 0||T.call(l,g.id)},children:"\u7F16\u8F91"},"editable")]}}],hn={ascend:"asc",descend:"desc"},kn={updatedAt:"updated_at",createdAt:"created_at"},Ie=function(){var o=k()(_()().mark(function p(){var g,u,l,h,T,Z,L,Oe,Ue,De,He,Be,Ye,dn,Nn,$e=arguments;return _()().wrap(function(fe){for(;;)switch(fe.prev=fe.next){case 0:if(g=$e.length>0&&$e[0]!==void 0?$e[0]:{},u=g.filtersValue,l=g.current,h=g.pageSize,T=$e.length>1?$e[1]:void 0,i){fe.next=4;break}return fe.abrupt("return",{data:[],total:0,success:!0});case 4:return Z={orderCondition:"updated_at",sort:"desc"},T&&(L=Object.entries(T)[0],L&&(Oe=c()(L,2),Ue=Oe[0],De=Oe[1],Ue&&De&&(Z={orderCondition:kn[Ue]||Ue,sort:hn[De]||"desc"}))),b(!0),fe.next=9,rn(a()({agentId:i,chatMemoryFilter:u||D,current:l||1,pageSize:h},Z));case 9:return He=fe.sent,b(!1),Be=He.data,Ye=Be.list,dn=Be.total,Nn=Be.pageNum,oe(Ye),Pe({pageSize:h,current:Nn,total:dn}),fe.abrupt("return",{data:Ye,total:dn,success:!0});case 15:case"end":return fe.stop()}},p)}));return function(){return o.apply(this,arguments)}}(),z={onChange:function(p){ae(p)}},q=function(){var o=k()(_()().mark(function p(g,u){return _()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,we(u);case 2:case"end":return h.stop()}},p)}));return function(g,u){return o.apply(this,arguments)}}();return(0,e.jsxs)("div",{className:t.memorySection,children:[(0,e.jsxs)("div",{className:t.filterSection,children:[(0,e.jsxs)("div",{className:t.filterItem,children:[(0,e.jsx)("div",{className:t.filterItemTitle,children:"\u7528\u6237\u95EE\u9898"}),(0,e.jsx)(j.default,{className:t.filterItemControl,placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u95EE\u9898",value:J,onChange:function(p){ne(a()(a()({},D),{},{question:p.target.value}))}})]}),(0,e.jsxs)("div",{className:t.filterItem,children:[(0,e.jsx)("div",{className:t.filterItemTitle,children:"\u5927\u6A21\u578B\u8BC4\u4F30\u7ED3\u679C"}),(0,e.jsx)(I.default,{className:t.filterItemControl,placeholder:"\u8BF7\u9009\u62E9\u5927\u6A21\u578B\u8BC4\u4F30\u7ED3\u679C",options:[{label:"\u6B63\u786E",value:xe.POSITIVE},{label:"\u9519\u8BEF",value:xe.NEGATIVE}],value:y,allowClear:!0,onChange:function(p){ne(a()(a()({},D),{},{llmReviewRet:p}))}})]}),(0,e.jsxs)("div",{className:t.filterItem,children:[(0,e.jsx)("div",{className:t.filterItemTitle,children:"\u7BA1\u7406\u5458\u8BC4\u4F30\u7ED3\u679C"}),(0,e.jsx)(I.default,{className:t.filterItemControl,placeholder:"\u8BF7\u9009\u62E9\u7BA1\u7406\u5458\u8BC4\u4F30\u7ED3\u679C",options:[{label:"\u6B63\u786E",value:xe.POSITIVE},{label:"\u9519\u8BEF",value:xe.NEGATIVE}],value:S,allowClear:!0,onChange:function(p){ne(a()(a()({},D),{},{humanReviewRet:p}))}})]}),(0,e.jsxs)("div",{className:t.filterItem,children:[(0,e.jsx)("div",{className:t.filterItemTitle,children:"\u72B6\u6001"}),(0,e.jsx)(I.default,{className:t.filterItemControl,placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",options:[{label:"\u5F85\u5B9A",value:P.PENDING},{label:"\u5DF2\u542F\u7528",value:P.ENABLED},{label:"\u5DF2\u7981\u7528",value:P.DISABLED}],value:A,allowClear:!0,onChange:function(p){ne(a()(a()({},D),{},{status:p}))}})]})]}),(0,e.jsxs)("div",{className:t.search,children:[(0,e.jsx)(X.ZP,{onClick:function(){return ne({})},children:"\u91CD\u7F6E"}),(0,e.jsx)(X.ZP,{type:"primary",onClick:function(){return Ie()},children:"\u67E5\u8BE2"}),i&&(0,e.jsx)(X.ZP,{type:"primary",onClick:function(){f(!0)},children:"\u65B0\u589E"}),(0,e.jsx)(X.ZP,{type:"primary",disabled:!(0,$.gP)(F),onClick:function(){Ze(F)},children:"\u6279\u91CF\u5220\u9664"},"batchDelete")]}),(0,e.jsx)(Vn.Z,{rowKey:"id",recordCreatorProps:!1,loading:ee,columns:ze,request:Ie,rowSelection:a()({type:"checkbox"},z),value:Ae,onChange:oe,pagination:nn,sticky:{offsetHeader:0},editable:{type:"multiple",editableKeys:re,actionRender:function(p,g,u){return[u.save,u.cancel]},onSave:q,onChange:he}}),Le&&i&&(0,e.jsx)(zn,{open:!0,agentId:i,onCancel:function(){f(!1)},onSubmit:function(){f(!1),Ie()}})]})},Kn=$n,Ln=n(61902),Un=n(51308),fn=v.Z.Item,Yn=function(x){var i=x.currentAgent,d=x.onSaveAgent,K=(0,r.useState)(!0),re=c()(K,2),he=re[0],B=re[1],te=v.Z.useForm(),Ae=c()(te,1),oe=Ae[0];(0,r.useEffect)(function(){oe.setFieldsValue(a()({},i)),B((i==null?void 0:i.isOpen)===1)},[]);var Y=function(){var V=k()(_()().mark(function ee(){var b,Q,w,D,ne,ke,_e,Le,f;return _()().wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.next=2,oe.validateFields();case 2:b=A.sent,Q=b.admins,w=b.adminOrgs,D=b.isOpen,ne=b.viewOrgs,ke=ne===void 0?[]:ne,_e=b.viewers,Le=_e===void 0?[]:_e,f=a()(a()({},i||{}),{},{admins:Q,adminOrgs:w,viewOrgs:ke,viewers:Le,isOpen:D?1:0}),d(f);case 6:case"end":return A.stop()}},ee)}));return function(){return V.apply(this,arguments)}}();return(0,e.jsxs)(v.Z,{form:oe,layout:"vertical",onValuesChange:function(ee){var b=ee.isOpen;b!==void 0&&B(b),Y()},className:t.permissionSection,children:[(0,e.jsx)(fn,{name:"admins",label:(0,e.jsx)(Un.Z,{title:"\u7BA1\u7406\u5458",subTitle:"\u7BA1\u7406\u5458\u5C06\u62E5\u6709\u4E3B\u9898\u57DF\u4E0B\u6240\u6709\u7F16\u8F91\u53CA\u8BBF\u95EE\u6743\u9650"}),children:(0,e.jsx)(cn.Z,{placeholder:"\u8BF7\u9080\u8BF7\u56E2\u961F\u6210\u5458"})}),(0,e.jsx)(fn,{name:"adminOrgs",label:"\u6309\u7EC4\u7EC7",children:(0,e.jsx)(Ln.Z,{type:"selectedDepartment",treeSelectProps:{placeholder:"\u8BF7\u9009\u62E9\u9700\u8981\u6388\u6743\u7684\u90E8\u95E8"}})}),(0,e.jsx)(v.Z.Item,{label:(0,e.jsx)(Un.Z,{title:"\u8BBE\u4E3A\u516C\u5F00"}),name:"isOpen",valuePropName:"checked",children:(0,e.jsx)(H.Z,{})}),!he&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(fn,{name:"viewOrgs",label:"\u6309\u7EC4\u7EC7",children:(0,e.jsx)(Ln.Z,{type:"selectedDepartment",treeSelectProps:{placeholder:"\u8BF7\u9009\u62E9\u9700\u8981\u6388\u6743\u7684\u90E8\u95E8"}})}),(0,e.jsx)(fn,{name:"viewers",label:"\u6309\u4E2A\u4EBA",children:(0,e.jsx)(cn.Z,{placeholder:"\u8BF7\u9009\u62E9\u9700\u8981\u6388\u6743\u7684\u4E2A\u4EBA"})})]})]})},Qn=Yn,je=v.Z.Item,Jn=j.default.TextArea,Hn={simpleMode:!1,debugMode:!0},Xn=function(x){var i,d=x.editAgent,K=x.onSaveAgent,re=x.onCreateToolBtnClick,he=(0,r.useState)(!1),B=c()(he,2),te=B[0],Ae=B[1],oe=(0,r.useState)([]),Y=c()(oe,2),V=Y[0],ee=Y[1],b=(0,r.useState)("basic"),Q=c()(b,2),w=Q[0],D=Q[1],ne=(0,r.useState)([]),ke=c()(ne,2),_e=ke[0],Le=ke[1],f=(0,r.useState)([]),J=c()(f,2),A=J[0],y=J[1],S=(0,r.useState)(""),Ee=c()(S,2),Se=Ee[0],F=Ee[1],ae=(0,r.useState)({}),ge=c()(ae,2),ve=ge[0],se=ge[1],nn=(0,r.useState)({enableSearch:!0,modelConfig:{timeOut:60,provider:"OPEN_AI",temperature:0},toolConfig:a()({},Hn)}),Pe=c()(nn,2),Ze=Pe[0],ze=Pe[1],hn=v.Z.useForm(),kn=c()(hn,1),Ie=kn[0];(0,r.useEffect)(function(){if(d){var l=(0,$.o3)(d.toolConfig,{}),h=a()(a()({},d),{},{enableSearch:d.enableSearch!==0,enableFeedback:d.enableFeedback!==0,toolConfig:a()(a()({},Hn),l)});Ie.setFieldsValue(h),ze(h),d.examples&&ee(d.examples.map(function(T){return{id:(0,$.Vj)(),question:T}}))}else Ie.resetFields();q(d==null?void 0:d.chatAppConfig),z()},[d]);var z=function(){var l=k()(_()().mark(function h(){var T,Z,L,Oe;return _()().wrap(function(De){for(;;)switch(De.prev=De.next){case 0:return De.next=2,(0,vn.zH)();case 2:T=De.sent,Z=T.code,L=T.data,Z===200&&L?(Oe=L.map(function(He){return{label:He.name,value:He.id}}),y(Oe)):G.ZP.error("\u83B7\u53D6\u6A21\u578B\u573A\u666F\u7C7B\u578B\u5931\u8D25");case 6:case"end":return De.stop()}},h)}));return function(){return l.apply(this,arguments)}}(),q=function(){var l=k()(_()().mark(function h(){var T,Z,L,Oe,Ue,De,He,Be,Ye,dn=arguments;return _()().wrap(function($e){for(;;)switch($e.prev=$e.next){case 0:return T=dn.length>0&&dn[0]!==void 0?dn[0]:{},$e.next=3,(0,vn.wd)();case 3:Z=$e.sent,L=Z.code,Oe=Z.data,L===200&&Oe?(Ue=Object.keys(Oe).map(function(an){var fe=Oe[an];return T[an]&&(fe=T[an]),{label:fe.name,value:an,enable:fe.enable,description:fe.description,prompt:fe.prompt}}),De=Ue.findIndex(function(an){return an.value==="S2SQL_PARSER"}),De>=0&&Ue.splice(0,0,Ue.splice(De,1)[0]),He=Ue[0],He&&F(String(He.value)),Be=Object.keys(Oe).reduce(function(an,fe){var Bn=Oe[fe];return T[fe]&&(Bn=T[fe]),a()(a()({},an),{},Me()({},fe,Bn))},{}),se(Be),Ye=Ie.getFieldsValue(),Ie.setFieldsValue(a()(a()({},Ye),{},{chatAppConfig:Be})),Le(Ue)):G.ZP.error("\u83B7\u53D6\u6A21\u578B\u573A\u666F\u7C7B\u578B\u5931\u8D25");case 7:case"end":return $e.stop()}},h)}));return function(){return l.apply(this,arguments)}}(),o={labelCol:{span:8},wrapperCol:{span:16}},p=function(){var l=k()(_()().mark(function h(){var T,Z,L,Oe;return _()().wrap(function(De){for(;;)switch(De.prev=De.next){case 0:return De.next=2,Ie.validateFields();case 2:return L=De.sent,Ae(!0),Oe=(0,$.o3)(d==null?void 0:d.toolConfig,{}),De.next=7,K==null?void 0:K(a()(a()(a()({id:d==null?void 0:d.id},d||{}),L),{},{toolConfig:JSON.stringify(a()(a()(a()({},Oe),L.toolConfig),{},{debugMode:((T=L.toolConfig)===null||T===void 0?void 0:T.simpleMode)===!0?!1:(Z=L.toolConfig)===null||Z===void 0?void 0:Z.debugMode})),examples:V.map(function(He){return He.question}),enableSearch:L.enableSearch?1:0,enableFeedback:L.enableFeedback?1:0,enableFileUpload:L.enableFileUpload?1:0,enableMultiAgent:L.enableMultiAgent?1:0,chatAppConfig:Object.keys(ve).reduce(function(He,Be){var Ye;return a()(a()({},He),{},Me()({},Be,a()(a()({},ve[Be]),(Ye=L.chatAppConfig)!==null&&Ye!==void 0&&Ye[Be]?L.chatAppConfig[Be]:{})))},{})}));case 7:Ae(!1);case 8:case"end":return De.stop()}},h)}));return function(){return l.apply(this,arguments)}}(),g=["\u81EA\u5B9A\u4E49\u63D0\u793A\u8BCD\u6A21\u677F\u53EF\u5D4C\u5165\u4EE5\u4E0B\u53D8\u91CF\uFF0C\u5C06\u7531\u7CFB\u7EDF\u81EA\u52A8\u8FDB\u884C\u66FF\u6362\uFF1A","-{{exemplar}} :\u66FF\u6362\u6210few-shot\u793A\u4F8B\uFF0C\u793A\u4F8B\u4E2A\u6570\u7531\u7CFB\u7EDF\u914D\u7F6E","-{{question}} :\u66FF\u6362\u6210\u7528\u6237\u95EE\u9898\uFF0C\u62FC\u63A5\u4E86\u4E00\u5B9A\u7684\u8865\u5145\u4FE1\u606F","-{{schema}} :\u66FF\u6362\u6210\u6570\u636E\u8BED\u4E49\u4FE1\u606F\uFF0C\u6839\u636E\u7528\u6237\u95EE\u9898\u6620\u5C04\u800C\u6765"],u=[{label:"\u57FA\u672C\u4FE1\u606F",key:"basic",children:(0,e.jsxs)("div",{className:t.agentFormContainer,children:[(0,e.jsx)(je,{name:"name",label:"\u540D\u79F0",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53\u540D\u79F0"}],children:(0,e.jsx)(j.default,{placeholder:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53\u540D\u79F0"})}),(0,e.jsx)(je,{name:"enableSearch",label:"\u5F00\u542F\u8F93\u5165\u8054\u60F3",valuePropName:"checked",htmlFor:"",children:(0,e.jsx)(H.Z,{})}),(0,e.jsx)(je,{name:"enableFeedback",label:"\u5F00\u542F\u7528\u6237\u786E\u8BA4",valuePropName:"checked",htmlFor:"",children:(0,e.jsx)(H.Z,{})}),(0,e.jsx)(je,{name:"enableFileUpload",label:"\u5F00\u542F\u6587\u4EF6\u4E0A\u4F20",valuePropName:"checked",htmlFor:"",children:(0,e.jsx)(H.Z,{})}),(0,e.jsx)(je,{name:"enableMultiAgent",label:"\u5F00\u542F\u591A\u667A\u80FD\u4F53",valuePropName:"checked",htmlFor:"",children:(0,e.jsx)(H.Z,{})}),(0,e.jsx)(je,{name:["toolConfig","simpleMode"],label:"\u5F00\u542F\u7CBE\u7B80\u6A21\u5F0F",tooltip:"\u7CBE\u7B80\u6A21\u5F0F\u4E0B\u4E0D\u53EF\u8C03\u6574\u67E5\u8BE2\u6761\u4EF6\u3001\u4E0D\u663E\u793A\u8C03\u8BD5\u4FE1\u606F\u3001\u4E0D\u663E\u793A\u53EF\u89C6\u5316\u7EC4\u4EF6",valuePropName:"checked",htmlFor:"",children:(0,e.jsx)(H.Z,{})}),(0,e.jsx)(je,{name:["toolConfig","debugMode"],label:"\u5F00\u542F\u8C03\u8BD5\u4FE1\u606F",hidden:(Ze==null||(i=Ze.toolConfig)===null||i===void 0?void 0:i.simpleMode)===!0,tooltip:"\u5305\u542BSchema\u6620\u5C04\u3001SQL\u751F\u6210\u6BCF\u9636\u6BB5\u7684\u5173\u952E\u4FE1\u606F",valuePropName:"checked",htmlFor:"",children:(0,e.jsx)(H.Z,{})}),(0,e.jsx)(je,{name:"admins",label:"\u7BA1\u7406\u5458",children:(0,e.jsx)(cn.Z,{placeholder:"\u8BF7\u9080\u8BF7\u56E2\u961F\u6210\u5458"})}),(0,e.jsx)(je,{tooltip:"\u9009\u62E9\u7528\u6237\u540E\uFF0C\u8BE5\u667A\u80FD\u4F53\u53EA\u5BF9\u6240\u9009\u7528\u6237\u53EF\u89C1",name:"viewers",label:"\u4F7F\u7528\u8005",children:(0,e.jsx)(cn.Z,{placeholder:"\u8BF7\u9080\u8BF7\u56E2\u961F\u6210\u5458"})}),(0,e.jsx)(je,{name:"examples",label:"\u793A\u4F8B\u95EE\u9898",children:(0,e.jsxs)("div",{className:t.paramsSection,children:[V.map(function(l){var h=l.id,T=l.question;return(0,e.jsxs)("div",{className:t.filterRow,children:[(0,e.jsx)(j.default,{placeholder:"\u793A\u4F8B\u95EE\u9898",value:T,className:t.questionExample,onChange:function(L){l.question=L.target.value,ee(C()(V))},allowClear:!0}),(0,e.jsx)(Ke.Z,{onClick:function(){ee(V.filter(function(L){return L.id!==h}))}})]},h)}),(0,e.jsxs)(X.ZP,{onClick:function(){ee([].concat(C()(V),[{id:(0,$.Vj)()}]))},children:[(0,e.jsx)(de.Z,{}),"\u65B0\u589E\u793A\u4F8B\u95EE\u9898"]})]})}),(0,e.jsx)(je,{name:"description",label:"\u63CF\u8FF0",children:(0,e.jsx)(Jn,{placeholder:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53\u63CF\u8FF0"})})]})},{label:"\u5927\u6A21\u578B\u914D\u7F6E",key:"modelConfig",children:(0,e.jsxs)("div",{className:t.agentFormContainer,style:{width:"1200px",marginTop:20},children:[(0,e.jsx)("div",{className:t.agentFormTitle,children:(0,e.jsxs)(on.Z,{children:["\u5E94\u7528\u573A\u666F ",(0,e.jsx)(bn,{})]})}),(0,e.jsxs)(on.Z,{style:{alignItems:"start"},children:[(0,e.jsx)("div",{style:{width:350},children:_e.map(function(l){return(0,e.jsx)("div",{className:"".concat(t.agentChatModelCell," ").concat(Se===l.value?t.agentChatModelCellActive:""),onClick:function(){F(String(l.value))},children:(0,e.jsx)(je,{name:["chatAppConfig",l.value,"enable"],label:l.label,valuePropName:"checked",tooltip:l.description,htmlFor:"",children:(0,e.jsx)(H.Z,{onChange:function(T){if(l.value==="INTENT_RECOGNITION"&&T){var Z,L=Ie.getFieldsValue();Ie.setFieldsValue(a()(a()({},L),{},{chatAppConfig:a()(a()({},L.chatAppConfig),{},{SMALL_TALK:a()(a()({},(Z=L.chatAppConfig)===null||Z===void 0?void 0:Z.SMALL_TALK),{},{enable:!0})})})),setTimeout(function(){F("SMALL_TALK")},0)}}})})})})}),(0,e.jsx)("div",{style:{width:900},children:_e.map(function(l){return(0,e.jsxs)("div",{style:{display:Se===l.value?"block":"none"},children:[(0,e.jsx)(je,{name:["chatAppConfig",l.value,"chatModelId"],label:"\u5E94\u7528\u6A21\u578B",tooltip:l.description,children:(0,e.jsx)(I.default,{placeholder:"",options:A})}),(0,e.jsx)(je,{name:["chatAppConfig",l.value,"prompt"],label:(0,e.jsx)(e.Fragment,{children:(0,e.jsxs)(on.Z,{children:["\u63D0\u793A\u8BCD\u6A21\u677F",(0,e.jsx)(Fe.Z,{overlayInnerStyle:{width:400},title:(0,e.jsx)(e.Fragment,{children:g.map(function(h){return(0,e.jsx)("div",{children:h})})}),children:(0,e.jsx)(en.Z,{})})]})}),children:(0,e.jsx)(j.default.TextArea,{style:{minHeight:600}})})]},"setting-".concat(l.value))})})]})]})},{label:"\u5DE5\u5177\u914D\u7F6E",key:"tools",children:(0,e.jsx)(pn,{currentAgent:d,onSaveAgent:K})},{label:"\u8BB0\u5FC6\u7BA1\u7406",key:"memory",children:(0,e.jsx)(Kn,{agentId:d==null?void 0:d.id})},{label:"\u6743\u9650\u7BA1\u7406",key:"permissonSetting",children:(0,e.jsx)(Qn,{currentAgent:d,onSaveAgent:K})}];return(0,e.jsx)(v.Z,a()(a()({},o),{},{form:Ie,initialValues:Ze,onValuesChange:function(h,T){ze(T)},className:On.Z.hchatdataForm,children:(0,e.jsx)(yn.Z,{tabBarExtraContent:(0,e.jsxs)(on.Z,{children:[w!=="memory"&&w!=="permissonSetting"&&(0,e.jsx)(X.ZP,{type:"primary",loading:te,onClick:function(){p()},children:"\u4FDD \u5B58"}),w==="tools"&&(0,e.jsxs)(X.ZP,{type:"primary",onClick:function(){re==null||re()},children:[(0,e.jsx)(de.Z,{})," \u65B0\u589E\u5DE5\u5177"]})]}),defaultActiveKey:"basic",activeKey:w,onChange:function(h){D(h)},items:u})}))},ea=Xn,na=function(x){var i=x.currentAgent,d=x.onSaveAgent,K=x.onCreateToolBtnClick,re=x.goBack;return(0,e.jsxs)("div",{className:t.toolsSection,children:[(0,e.jsxs)("div",{className:t.toolsSectionTitleBar,children:[(0,e.jsx)(mn.Z,{className:t.backIcon,onClick:re}),(0,e.jsx)("div",{className:t.agentTitle,children:i==null?void 0:i.name}),(0,e.jsxs)("div",{className:t.toggleStatus,children:[(i==null?void 0:i.status)===0?"\u5DF2\u7981\u7528":(0,e.jsx)("span",{className:t.online,children:"\u5DF2\u542F\u7528"}),(0,e.jsx)("span",{onClick:function(B){B.stopPropagation()},children:(0,e.jsx)(H.Z,{size:"small",defaultChecked:(i==null?void 0:i.status)===1,onChange:function(B){d(a()(a()({},i),{},{status:B?1:0}),!0)}})})]})]}),(0,e.jsx)("div",{className:t.basicInfo,children:(0,e.jsx)(ea,{onSaveAgent:d,editAgent:i,onCreateToolBtnClick:K})})]})},aa=na,sa=function(){var x=(0,r.useState)([]),i=c()(x,2),d=i[0],K=i[1],re=(0,r.useState)(!1),he=c()(re,2),B=he[0],te=he[1],Ae=(0,r.useState)(),oe=c()(Ae,2),Y=oe[0],V=oe[1],ee=(0,r.useState)(!1),b=c()(ee,2),Q=b[0],w=b[1],D=(0,r.useState)(!1),ne=c()(D,2),ke=ne[0],_e=ne[1],Le=(0,r.useState)({}),f=c()(Le,2),J=f[0],A=f[1];(0,r.useEffect)(function(){var F=(0,$.o3)(Y==null?void 0:Y.toolConfig,{});A(F)},[Y]);var y=function(){var F=k()(_()().mark(function ae(){var ge;return _()().wrap(function(se){for(;;)switch(se.prev=se.next){case 0:return te(!0),se.next=3,_n();case 3:ge=se.sent,te(!1),K(ge.data||[]);case 6:case"end":return se.stop()}},ae)}));return function(){return F.apply(this,arguments)}}();(0,r.useEffect)(function(){y()},[]);var S=function(){var F=k()(_()().mark(function ae(ge){var ve,se;return _()().wrap(function(Pe){for(;;)switch(Pe.prev=Pe.next){case 0:if(ve=J||{},ve.tools||(ve.tools=[]),ge.id?(se=ve.tools.findIndex(function(Ze){return Ze.id===ge.id}),ve.tools[se]=ge):ve.tools.push(a()(a()({},ge),{},{id:(0,$.Vj)()})),A(ve),Y!=null&&Y.id){Pe.next=8;break}return V(a()(a()({},Y),{},{toolConfig:JSON.stringify(ve)})),w(!1),Pe.abrupt("return");case 8:return Pe.next=10,Ee(a()(a()({},Y),{},{toolConfig:JSON.stringify(ve)}));case 10:w(!1);case 11:case"end":return Pe.stop()}},ae)}));return function(ge){return F.apply(this,arguments)}}(),Ee=function(){var F=k()(_()().mark(function ae(ge,ve){var se,nn,Pe;return _()().wrap(function(ze){for(;;)switch(ze.prev=ze.next){case 0:return ze.next=2,Ne(ge);case 2:se=ze.sent,nn=se.data,Pe=se.code,Pe===200&&(V(a()({},nn)),y()),ve||G.ZP.success("\u4FDD\u5B58\u6210\u529F");case 7:case"end":return ze.stop()}},ae)}));return function(ge,ve){return F.apply(this,arguments)}}(),Se=function(){var F=k()(_()().mark(function ae(ge){return _()().wrap(function(se){for(;;)switch(se.prev=se.next){case 0:return se.next=2,We(ge);case 2:G.ZP.success("\u5220\u9664\u6210\u529F"),y();case 4:case"end":return se.stop()}},ae)}));return function(ge){return F.apply(this,arguments)}}();return(0,e.jsxs)("div",{className:t.agent,children:[ke?(0,e.jsx)(aa,{currentAgent:Y,onSaveAgent:Ee,onCreateToolBtnClick:function(){w(!0)},goBack:function(){_e(!1),V(void 0)}}):(0,e.jsx)(ye,{agents:d,loading:B,onSelectAgent:function(ae){V(ae),_e(!0)},onDeleteAgent:Se,onSaveAgent:Ee,onCreatBtnClick:function(){V(void 0),_e(!0)}}),Q&&(0,e.jsx)(Xe,{onSaveTool:S,onCancel:function(){w(!1)}})]})},ta=sa},87411:function(be,W,n){"use strict";n.d(W,{UV:function(){return ie},Z7:function(){return k},f$:function(){return _},fZ:function(){return ce},iS:function(){return a}});var E=n(20221);function a(U){return(0,E.request)("/api/chat/plugin",{method:U.id?"PUT":"POST",data:U})}function ie(U){return(0,E.request)("/api/chat/plugin/query",{method:"POST",data:U})}function _(U){return(0,E.request)("/api/chat/plugin/".concat(U),{method:"DELETE"})}function ce(){return(0,E.request)("/api/chat/conf/getDomainDataSetTree",{method:"GET"})}function k(U){return(0,E.request)("/api/chat/conf/getDataSetSchema/".concat(U),{method:"GET"})}},4845:function(be,W,n){"use strict";n.d(W,{aI:function(){return k},wd:function(){return _},yC:function(){return a},zH:function(){return ce}});var E=n(43047);function a(U){return(0,E.ZP)("".concat("/api/chat/","model/testConnection"),{method:"POST",data:U})}function ie(){return request("".concat("/api/chat/","model/getModelTypeList"),{method:"GET"})}function _(){return(0,E.ZP)("".concat("/api/chat/","model/getModelAppList"),{method:"GET"})}function ce(){return(0,E.ZP)("".concat("/api/chat/","model/getModelList"),{method:"GET"})}function k(){return(0,E.ZP)("".concat("/api/chat/","model/getModelParameters"),{method:"GET"})}},98121:function(be,W){"use strict";W.Z={root:"root___GKj2d",colorWeak:"colorWeak___mio8j","ant-layout":"ant-layout___jAUUr","ant-pro-layout":"ant-pro-layout___mMCOO","ant-layout-content":"ant-layout-content___wlTrJ","ant-pro-layout-content":"ant-pro-layout-content___aNsFI","markdown-body":"markdown-body___EgDMK","ant-table":"ant-table___najh6","ant-table-thead":"ant-table-thead___k7etk","ant-table-tbody":"ant-table-tbody___UlQsl","ant-design-pro":"ant-design-pro___YYeXb","ant-modal-body":"ant-modal-body___zVUws","ant-pro-layout-bg-list":"ant-pro-layout-bg-list___l5Pm8",ellipsis:"ellipsis___rEr4d","ant-spin":"ant-spin___zBTAL","ant-spin-dot":"ant-spin-dot___yFdYs","ant-pro-layout-header":"ant-pro-layout-header___geaEO","ant-menu":"ant-menu___zPGo9","ant-menu-root":"ant-menu-root___AJ3JZ","ant-menu-item":"ant-menu-item___D6rxL","ant-menu-item-selected":"ant-menu-item-selected___Hl_pa","ant-menu-light":"ant-menu-light___DFiaY","ant-menu-horizontal":"ant-menu-horizontal___Cic5G","ant-menu-submenu-selected":"ant-menu-submenu-selected___ZjHBm","ant-pro-table-list-toolbar-container-mobile":"ant-pro-table-list-toolbar-container-mobile___UTPP0",logo:"logo___ljDSQ","ai-logo":"ai-logo___ER1PY","ai-text":"ai-text___Qe7AQ","data-text":"data-text___d5Eed",g6ContextMenuContainer:"g6ContextMenuContainer___x13eR","ant-tag":"ant-tag___MiqcU","semantic-graph-toolbar":"semantic-graph-toolbar___Ut4ZU","g6-component-tooltip":"g6-component-tooltip___Epj8f","inherit-from-model-row":"inherit-from-model-row___VXoml","ant-table-cell-row-hover":"ant-table-cell-row-hover___WUyJS","ant-form-item":"ant-form-item___i4hjr","ant-form-item-label":"ant-form-item-label___tlwus","ant-select-dropdown":"ant-select-dropdown___b5Edi","ant-select-item":"ant-select-item___IfbTN","ant-select-item-option-selected":"ant-select-item-option-selected___oKGPP","ant-select-item-option-disabled":"ant-select-item-option-disabled___eEpf8",hchatdataForm:"hchatdataForm___G7Ted",aiPulse:"aiPulse___ekm0v"}},17258:function(be,W,n){"use strict";n.d(W,{Z:function(){return Je}});var E=n(44194),a=n(51865),ie=n.n(a),_=n(34573),ce=n(9695),k=n(71841),U=n(11778),c=n(26833),G=n(47506),r=n(40044),de=n(87471),H=n(19107),Te=n(77167),X=n(88370);const sn=s=>{const{paddingXXS:O,lineWidth:M,tagPaddingHorizontal:C,componentCls:v,calc:le}=s,I=le(C).sub(M).equal(),j=le(O).sub(M).equal();return{[v]:Object.assign(Object.assign({},(0,H.Wf)(s)),{display:"inline-block",height:"auto",marginInlineEnd:s.marginXS,paddingInline:I,fontSize:s.tagFontSize,lineHeight:s.tagLineHeight,whiteSpace:"nowrap",background:s.defaultBg,border:`${(0,r.unit)(s.lineWidth)} ${s.lineType} ${s.colorBorder}`,borderRadius:s.borderRadiusSM,opacity:1,transition:`all ${s.motionDurationMid}`,textAlign:"start",position:"relative",[`&${v}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:s.defaultColor},[`${v}-close-icon`]:{marginInlineStart:j,fontSize:s.tagIconSize,color:s.colorIcon,cursor:"pointer",transition:`all ${s.motionDurationMid}`,"&:hover":{color:s.colorTextHeading}},[`&${v}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${s.iconCls}-close, ${s.iconCls}-close:hover`]:{color:s.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${v}-checkable-checked):hover`]:{color:s.colorPrimary,backgroundColor:s.colorFillSecondary},"&:active, &-checked":{color:s.colorTextLightSolid},"&-checked":{backgroundColor:s.colorPrimary,"&:hover":{backgroundColor:s.colorPrimaryHover}},"&:active":{backgroundColor:s.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${s.iconCls} + span, > span + ${s.iconCls}`]:{marginInlineStart:I}}),[`${v}-borderless`]:{borderColor:"transparent",background:s.tagBorderlessBg}}},Ce=s=>{const{lineWidth:O,fontSizeIcon:M,calc:C}=s,v=s.fontSizeSM;return(0,Te.mergeToken)(s,{tagFontSize:v,tagLineHeight:(0,r.unit)(C(s.lineHeightSM).mul(v).equal()),tagIconSize:C(M).sub(C(O).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:s.defaultBg})},Ve=s=>({defaultBg:new de.FastColor(s.colorFillQuaternary).onBackground(s.colorBgContainer).toHexString(),defaultColor:s.colorText});var t=(0,X.I$)("Tag",s=>{const O=Ce(s);return sn(O)},Ve),e=function(s,O){var M={};for(var C in s)Object.prototype.hasOwnProperty.call(s,C)&&O.indexOf(C)<0&&(M[C]=s[C]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,C=Object.getOwnPropertySymbols(s);v<C.length;v++)O.indexOf(C[v])<0&&Object.prototype.propertyIsEnumerable.call(s,C[v])&&(M[C[v]]=s[C[v]]);return M},ye=E.forwardRef((s,O)=>{const{prefixCls:M,style:C,className:v,checked:le,onChange:I,onClick:j}=s,me=e(s,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:N,tag:pe}=E.useContext(G.E_),ue=qe=>{I==null||I(!le),j==null||j(qe)},P=N("tag",M),[xe,Ke,Ge]=t(P),Re=ie()(P,`${P}-checkable`,{[`${P}-checkable-checked`]:le},pe==null?void 0:pe.className,v,Ke,Ge);return xe(E.createElement("span",Object.assign({},me,{ref:O,style:Object.assign(Object.assign({},C),pe==null?void 0:pe.style),className:Re,onClick:ue})))}),$=n(14325);const R=s=>(0,$.Z)(s,(O,{textColor:M,lightBorderColor:C,lightColor:v,darkColor:le})=>({[`${s.componentCls}${s.componentCls}-${O}`]:{color:M,background:v,borderColor:C,"&-inverse":{color:s.colorTextLightSolid,background:le,borderColor:le},[`&${s.componentCls}-borderless`]:{borderColor:"transparent"}}}));var _n=(0,X.bk)(["Tag","preset"],s=>{const O=Ce(s);return R(O)},Ve);function Ne(s){return typeof s!="string"?s:s.charAt(0).toUpperCase()+s.slice(1)}const We=(s,O,M)=>{const C=Ne(M);return{[`${s.componentCls}${s.componentCls}-${O}`]:{color:s[`color${M}`],background:s[`color${C}Bg`],borderColor:s[`color${C}Border`],[`&${s.componentCls}-borderless`]:{borderColor:"transparent"}}}};var Qe=(0,X.bk)(["Tag","status"],s=>{const O=Ce(s);return[We(O,"success","Success"),We(O,"processing","Info"),We(O,"error","Error"),We(O,"warning","Warning")]},Ve),ln=function(s,O){var M={};for(var C in s)Object.prototype.hasOwnProperty.call(s,C)&&O.indexOf(C)<0&&(M[C]=s[C]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,C=Object.getOwnPropertySymbols(s);v<C.length;v++)O.indexOf(C[v])<0&&Object.prototype.propertyIsEnumerable.call(s,C[v])&&(M[C[v]]=s[C[v]]);return M};const we=E.forwardRef((s,O)=>{const{prefixCls:M,className:C,rootClassName:v,style:le,children:I,icon:j,color:me,onClose:N,bordered:pe=!0,visible:ue}=s,P=ln(s,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:xe,direction:Ke,tag:Ge}=E.useContext(G.E_),[Re,qe]=E.useState(!0),Xe=(0,_.Z)(P,["closeIcon","closable"]);E.useEffect(()=>{ue!==void 0&&qe(ue)},[ue]);const mn=(0,ce.o2)(me),un=(0,ce.yT)(me),Me=mn||un,on=Object.assign(Object.assign({backgroundColor:me&&!Me?me:void 0},Ge==null?void 0:Ge.style),le),Fe=xe("tag",M),[yn,An,Sn]=t(Fe),gn=ie()(Fe,Ge==null?void 0:Ge.className,{[`${Fe}-${me}`]:Me,[`${Fe}-has-color`]:me&&!Me,[`${Fe}-hidden`]:!Re,[`${Fe}-rtl`]:Ke==="rtl",[`${Fe}-borderless`]:!pe},C,v,An,Sn),En=pn=>{pn.stopPropagation(),N==null||N(pn),!pn.defaultPrevented&&qe(!1)},[,bn]=(0,k.Z)((0,k.w)(s),(0,k.w)(Ge),{closable:!1,closeIconRender:pn=>{const On=E.createElement("span",{className:`${Fe}-close-icon`,onClick:En},pn);return(0,U.wm)(pn,On,en=>({onClick:cn=>{var vn;(vn=en==null?void 0:en.onClick)===null||vn===void 0||vn.call(en,cn),En(cn)},className:ie()(en==null?void 0:en.className,`${Fe}-close-icon`)}))}}),Tn=typeof P.onClick=="function"||I&&I.type==="a",Pn=j||null,Rn=Pn?E.createElement(E.Fragment,null,Pn,I&&E.createElement("span",null,I)):I,Dn=E.createElement("span",Object.assign({},Xe,{ref:O,className:gn,style:on}),Rn,bn,mn&&E.createElement(_n,{key:"preset",prefixCls:Fe}),un&&E.createElement(Qe,{key:"status",prefixCls:Fe}));return yn(Tn?E.createElement(c.Z,{component:"Tag"},Dn):Dn)});we.CheckableTag=ye;var Je=we},49841:function(be){function W(n){if(n==null)throw new TypeError("Cannot destructure "+n)}be.exports=W,be.exports.__esModule=!0,be.exports.default=be.exports}}]);
