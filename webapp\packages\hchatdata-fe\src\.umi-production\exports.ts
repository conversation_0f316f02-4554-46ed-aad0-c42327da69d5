// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
// defineApp
export { defineApp } from './core/defineApp'
export type { RuntimeConfig } from './core/defineApp'
// plugins
export { Access, useAccess, useAccessMarkedRoutes } from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-access';
export { addLocale, setLocale, getLocale, getIntl, useIntl, injectIntl, formatMessage, FormattedMessage, getAllLocales, FormattedDate, FormattedDateParts, FormattedDisplayName, FormattedHTMLMessage, FormattedList, FormattedNumber, FormattedNumberParts, FormattedPlural, FormattedRelativeTime, FormattedTime, FormattedTimeParts, IntlProvider, RawIntlProvider, SelectLang } from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-locale';
export { Provider, useModel } from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-model';
export { useRequest, UseRequestProvider, request, getRequestInstance } from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-request';
// plugins types.d.ts
export * from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-access/types.d';
export * from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-antd/types.d';
export * from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-layout/types.d';
export * from 'D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-request/types.d';
// @umijs/renderer-*
export { createBrowserHistory, createHashHistory, createMemoryHistory, Helmet, HelmetProvider, createSearchParams, generatePath, matchPath, matchRoutes, Navigate, NavLink, Outlet, resolvePath, useLocation, useMatch, useNavigate, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes, useSearchParams, useAppData, useClientLoaderData, useLoaderData, useRouteProps, useSelectedRoutes, useServerLoaderData, renderClient, __getRoot, Link, useRouteData, __useFetcher, withRouter } from 'D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+renderer-react@4.4.11_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@umijs/renderer-react';
export type { History, ClientLoader } from 'D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+renderer-react@4.4.11_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@umijs/renderer-react'
// umi/client/client/plugin
export { ApplyPluginsType, PluginManager } from 'D:/项目/xx/HChatData/webapp/node_modules/.pnpm/umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@18.3.1_eslint@8.35.0_jest@27._d5pvzqomnp5az5zle5uryp6pkq/node_modules/umi/client/client/plugin.js';
export { history, createHistory } from './core/history';
export { terminal } from './core/terminal';
// react ssr
export const useServerInsertedHTML: Function = () => {};
