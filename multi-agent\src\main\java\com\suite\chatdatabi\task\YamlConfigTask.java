package com.suite.chatdatabi.task;

import com.suite.chatdatabi.common.config.EmbeddingConfig;
import com.suite.chatdatabi.common.pojo.DataItem;
import com.suite.chatdatabi.common.service.EmbeddingService;
import com.suite.chatdatabi.config.GenieConfig;
import com.suite.chatdatabi.headless.server.service.DimensionService;
import com.suite.chatdatabi.headless.server.service.MetricService;
import dev.langchain4j.inmemory.spring.InMemoryEmbeddingStoreFactory;
import dev.langchain4j.store.embedding.EmbeddingStoreFactory;
import dev.langchain4j.store.embedding.EmbeddingStoreFactoryProvider;
import dev.langchain4j.store.embedding.TextSegmentConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;

@Component
@Slf4j
@Order(2)
public class YamlConfigTask {




//    /** *重新加载数据库配置文件 */
//    @Scheduled(cron = "* * */1 * * ?")
//    public void reloadMetaEmbedding() {
//        long startTime = System.currentTimeMillis();
//        try {
//            genieConfig.init();
//        } catch (Exception e) {
//            log.error("application.yml 配置文件加载错误.", e);
//        }
////        long duration = System.currentTimeMillis() - startTime;
////        log.info("application.yml 配置文件已重新加载，执行 {} 毫秒", duration);
//    }

//    @Override
//    public void run(String... args) throws Exception {
//        try {
//            reloadMetaEmbedding();
//        } catch (Exception e) {
//            log.error("initMetaEmbedding error", e);
//        }
//    }
}
